export type NLFormHandler = 'formaccounts' | 'formcascader' | 'formcheckbox' | 'formckeditor' | 'formcollapse' | 'formcube' | 'formdate' | 'formdivider' | 'formlabel' | 'formlink' | 'formnumber' | 'formpassword' | 'formradio' | 'formrate' | 'formscript' | 'formselect' | 'formtab' | 'formtableinputer' | 'formtableselector' | 'formtext' | 'formtextarea' | 'formtime' | 'formtreeselect' | 'formupload' | 'formuserselect';
export interface NLFormComponent {
  handler: NLFormHand<PERSON>;
  reaction: NLFormComponentReaction;
  override_config: NLFormComponentConfig;
  icon: string;
  hasValue: boolean;
  notUniqueKey?: boolean;
  label: string;
  type: string;
  category: string;
  config: NLFormComponentConfig;
  uuid: string;
  switchHandler?: string[];
  isDynamicValue?: boolean;
  key?: string;
  excludedFromCondition?: boolean;
  isContainer?: boolean;
  component?: NLFormComponent[];
}

export interface NLFormComponentConfig {
  isMask?: boolean;
  width: string;
  content?: string;
  isHide: boolean;
  isRequired?: boolean;
  isReadOnly?: boolean;
  defaultValue?: NLFormComponentDefaultValue | string;
  description?: string;
  isDisabled?: boolean;
  hiddenFieldList?: any[];
  mapping?: NLFormComponentMapping;
  isMultiple?: boolean;
  matrixUuid?: string;
  tableKey?: string;
  isAddData?: boolean;
  sourceColumnList?: any[];
  dataList?: (NLFormComponentDataList | NLFormComponentMapping)[];
  defaultValueType?: string;
  tagKey?: string;
  dataSource?: string;
  dataSourceType?: string;
  levelType?: number;
  direction?: string;
  typeList?: NLFormComponentMapping[];
  optionList?: NLFormComponentMapping[];
  disableDefaultValue?: boolean;
  hideHeaderWhenDataEmpty?: boolean;
  pageSize?: number;
  matrixType?: string;
  dataConfig?: NLFormComponentDataConfig[];
  mode?: string;
  needPage?: boolean;
  styleType?: string;
  format?: string;
  placeholder?: string;
  validValueList?: any[];
  text?: string;
  value?: string;
  target?: string;
  showText?: boolean;
  count?: number;
  allowHalf?: boolean;
  config?: {
    textName: string;
    valueName: string;
    url: string;
  };
  url?: string;
  isTemplate?: boolean;
  uploadType?: string;
  codeMode?: string;
  componentTopLeftTip?: string;
  groupList?: string[];
  dividerType?: string;
  dividerWidth?: number;
  isFontBold?: boolean;
  contentPosition?: string;
  fontColor?: string;
  dividerColor?: string;
  tabList?: NLFormComponentTabList[];
  isShowComponentNameInTab?: boolean;
  type?: string;
  isAccordion?: boolean;
  isSimple?: boolean;
  panelList?: NLFormComponentPaneList[];
  isAutoSelectdOnlyValue?: boolean;
}

export interface NLFormComponentPaneList {
  component: string[];
  text: string;
  value: string;
}

export interface NLFormComponentTabList {
  _visible: boolean;
  component: string[];
  text: string;
  value: string;
}

export interface NLFormComponentDataConfig {
  handler: string;
  matrixAttrUuid: string;
  isSearch: boolean;
  hasValue: boolean;
  label: string;
  isMobile: boolean;
  isSearchable: number;
  isPC: boolean;
  uuid: string;
  key: string;
  reaction?: NLFormComponentReaction;
  config?: {
    isRequired: boolean;
    isMask: boolean;
    isHide: boolean;
  };
}


export interface NLFormComponentDataList {
  children: NLFormComponentMapping[];
  text: string;
  value: string;
}


export interface NLFormComponentReaction {
  hide: Record<string, any>;
  display: Record<string, any>;
  mask?:Record<string, any>;
  readonly?:Record<string, any>;
  setvalue?:Record<string, any>;
  disable?:Record<string, any>;
  emit?:Record<string, any>;
  required?:Record<string, any>;
  clearValue?:Record<string, any>;
  filter?:Record<string, any>;
  setValueOther?:Record<string, any>;
}



export interface FormCustomExtendConfig {
  extendConfigList: any[];
}


export interface HideComponentList {
  handler: string;
  reaction: NLFormComponentReaction;
  isHideComponent: boolean;
  icon: string;
  hasValue: boolean;
  label: string;
  type: string;
  category: string;
  isDynamicValue?: boolean;
  config: NLFormComponentConfig;
  uuid: string;
  key: string;
}

export interface NLFormComponentMapping {
  text: string;
  value: string;
}

export interface NLFormComponentDefaultValue {
  _isHidden: boolean;
  _showtxtList: ShowtxtList[];
  text: string;
  value: string;
}

export interface ShowtxtList {
  Highlight: boolean;
  value: string;
}
