import { $t } from '@/resources/init.js';

export default {
  handler: 'formdivider',
  label: $t('term.framework.formdivider'),
  type: 'form',
  category: 'layout',
  icon: 'tsfont-minus',
  hasValue: false,
  override_config: {},
  notUniqueKey: true, //不需要唯一标识key
  config: {
    description: '',
    width: '100%',
    isHide: false,
    dividerColor: '',
    dividerType: 'solid',
    dividerWidth: 1,
    fontColor: '',
    isFontBold: false,
    content: '',
    contentPosition: 'left'
  },
  reaction: {
    hide: {},
    display: {}
  }
};
