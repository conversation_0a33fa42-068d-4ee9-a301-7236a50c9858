import type { NLFormComponent, HideComponentList} from './nl-form-components'

export interface NLCatagory {
  name: string;
  uuid: string;
  list: NLChannel[]
}

export interface NLChannel {
  startPage: number;
  color: string;
  icon: string;
  viewAuthorityList: any[];
  isActive: number;
  type: string;
  uuid: string;
  reportAuthorityList: any[];
  childrenCount: number;
  typeAndUuid: string;
  isDisplayPriority: number;
  policyLimit: number;
  channelTypeUuid: string;
  name: string;
  isActivePriority: number;
  support: string;
  desc: string;
  isFavorite: number;
}

export interface NLChannelSearchResponse {
  favoriteList: any[];
  list: NLCatagory[]
}


export interface ProcessTaskDraft {
  channelPath: string;
  channelType: ChannelType;
  channelUuid: string;
  channelVo: ChannelVo;
  commentList: any[];
  defaultPriorityUuid: string;
  formAttributeDataMap: Record<string, any>;
  formAttributeHideList: any[];
  formConfig: ProcessTaskFormConfig;
  isActivePriority: number;
  isDeleted: number;
  isDisplayPriority: number;
  isFocus: number;
  isHasOldFormProp: number;
  isShowBaseInfo: number;
  isShowProcessTaskStepCommentEditorToolbar: number;
  isShowStepList: number;
  policyLimit: number;
  priorityUuid: string;
  processDispatcherList: any[];
  processUuid: string;
  redoStepList: any[];
  source: string;
  sourceName: string;
  startPage: number;
  startProcessTaskStep: StartProcessTaskStep;
  stepList: any[];
  tranferReportProcessTaskList: any[];
  worktimeUuid: string;
}

export interface StartProcessTaskStep {
  actionList: any[];
  assignableWorkerStepList: any[];
  backwardNextStepList: any[];
  commentList: any[];
  formAttributeVoList: any[];
  formSceneUuid: string;
  forwardNextStepList: any[];
  handler: string;
  isActive: number;
  isAllDone: boolean;
  isCurrentUserDone: boolean;
  isNeedContent: number;
  isNeedUploadFile: number;
  isRequired: number;
  minorUserList: any[];
  name: string;
  policyLimit: number;
  processStepUuid: string;
  processTaskStepRemindList: any[];
  processTaskStepTask: Record<string, string>;
  processUuid: string;
  slaTimeList: any[];
  startPage: number;
  type: string;
  userList: any[];
  workerList: any[];
  workerPolicyList: any[];
}

export interface ProcessTaskFormConfig {
  formExtendConfig: FormExtendConfig;
  reaction: RowReaction;
  hideComponentList: HideComponentList[];
  _type: string;
  readOnly: boolean;
  lcd: number;
  uuid: string;
  defaultSceneUuid: string;
  headerList: HeaderList[];
  formCustomExtendConfig: any;
  hiddenRowList: any[];
  lefterList: LefterList[];
  name: string;
  tableList: TableList[];
  sceneList: any[];
  lcu: string;
}

export interface TableList {
  col: number;
  component?: NLFormComponent;
  row: number;
}

export interface LefterList {
  height: number;
}

export interface HeaderList {
  width: number;
}

export interface RowReaction {
  displayrow: Displayrow[];
  hiderow: Hiderow[];
}

export interface Hiderow {
  conditionGroupList: ConditionGroupList[];
  rows: number[];
  conditionGroupRelList: any[];
}

export interface ConditionGroupList {
  conditionList: ConditionList[];
  conditionRelList: any[];
  uuid: string;
}

export interface ConditionList {
  formItemUuid: string;
  expression: string;
  valueList: string[];
  uuid: string;
}

export interface Displayrow {
  rows: any[];
}

export interface FormExtendConfig {
  attributeList: any[];
}


export interface ChannelVo {
  channelTypeUuid: string;
  childrenCount: number;
  color: string;
  config: {
    channelRelationList: any[];
    allowTranferReport: number;
  };
  desc: string;
  icon: string;
  isActive: number;
  isActivePriority: number;
  isDisplayPriority: number;
  name: string;
  parentUuid: string;
  policyLimit: number;
  reportAuthorityList: any[];
  startPage: number;
  support: string;
  type: string;
  typeAndUuid: string;
  uuid: string;
  viewAuthorityList: any[];
}

export interface ChannelType {
  color: string;
  description: string;
  isActive: number;
  name: string;
  policyLimit: number;
  prefix: string;
  sort: number;
  startPage: number;
  uuid: string;
}