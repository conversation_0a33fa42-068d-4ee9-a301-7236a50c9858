export interface NLCatagory {
  name: string;
  uuid: string;
  list: NLChannel[]
}

export interface NLChannel {
  startPage: number;
  color: string;
  icon: string;
  viewAuthorityList: any[];
  isActive: number;
  type: string;
  uuid: string;
  reportAuthorityList: any[];
  childrenCount: number;
  typeAndUuid: string;
  isDisplayPriority: number;
  policyLimit: number;
  channelTypeUuid: string;
  name: string;
  isActivePriority: number;
  support: string;
  desc: string;
  isFavorite: number;
}

export interface NLChannelSearchResponse {
  favoriteList: any[];
  list: NLCatagory[]
}
