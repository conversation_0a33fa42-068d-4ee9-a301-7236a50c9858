export interface NLResultWrap<T = any> {
  code: number;
  message: 'string';
  data: T
}

export type NLResult<T = any> =  NLResultWrap<{
    Status: 'OK' | 'ERROR',
    TimeCost: number;
    Return: T
  }>

export type NLPaginationResult<T extends Record<string, Array<any>> = {list: any[]}> = NLResultWrap<{
  Status: 'OK' | 'ERROR';
  TimeCost: number;
  Return: {
    currentPage: number;
    pageCount: number;
    pageSize: number;
    rowNum: number;
  } & T;
}>

export type NLPaginationRequest<T = Record<string, any>> = {
  currentPage?: number;
  pageSize?: number;
} & Partial<T>;

