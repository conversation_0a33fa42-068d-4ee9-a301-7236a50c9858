[{"name": "a10", "css": "tsfont-a10", "hexCodepoint": "0xe800", "codepoint": 59392}, {"name": "accessendpoint", "css": "tsfont-accessendpoint", "hexCodepoint": "0xe801", "codepoint": 59393}, {"name": "action", "css": "tsfont-action", "hexCodepoint": "0xe802", "codepoint": 59394}, {"name": "activemq", "css": "tsfont-activemq", "hexCodepoint": "0xe803", "codepoint": 59395}, {"name": "adapter", "css": "tsfont-adapter", "hexCodepoint": "0xe804", "codepoint": 59396}, {"name": "addchange", "css": "tsfont-addchange", "hexCodepoint": "0xe805", "codepoint": 59397}, {"name": "addimg-s", "css": "tsfont-addimg-s", "hexCodepoint": "0xe806", "codepoint": 59398}, {"name": "addimg", "css": "tsfont-addimg", "hexCodepoint": "0xe807", "codepoint": 59399}, {"name": "addtag", "css": "tsfont-addtag", "hexCodepoint": "0xe808", "codepoint": 59400}, {"name": "addteam", "css": "tsfont-addteam", "hexCodepoint": "0xe809", "codepoint": 59401}, {"name": "adduser", "css": "tsfont-adduser", "hexCodepoint": "0xe80a", "codepoint": 59402}, {"name": "adjust", "css": "tsfont-adjust", "hexCodepoint": "0xe80b", "codepoint": 59403}, {"name": "agent", "css": "tsfont-agent", "hexCodepoint": "0xe80c", "codepoint": 59404}, {"name": "aix", "css": "tsfont-aix", "hexCodepoint": "0xe80d", "codepoint": 59405}, {"name": "alicloud", "css": "tsfont-alicloud", "hexCodepoint": "0xe80e", "codepoint": 59406}, {"name": "anquangeliwangzha", "css": "tsfont-anquangeliwangzha", "hexCodepoint": "0xe80f", "codepoint": 59407}, {"name": "anquanguanlipingtai", "css": "tsfont-anquanguanlipingtai", "hexCodepoint": "0xe810", "codepoint": 59408}, {"name": "anquanwangguan", "css": "tsfont-anquanwangguan", "hexCodepoint": "0xe811", "codepoint": 59409}, {"name": "anzhuangbao", "css": "tsfont-anzhuangbao", "hexCodepoint": "0xe812", "codepoint": 59410}, {"name": "<PERSON>z<PERSON>ngshuxing", "css": "tsfont-anzhuangshuxing", "hexCodepoint": "0xe813", "codepoint": 59411}, {"name": "apache", "css": "tsfont-apache", "hexCodepoint": "0xe814", "codepoint": 59412}, {"name": "app", "css": "tsfont-app", "hexCodepoint": "0xe815", "codepoint": 59413}, {"name": "apple", "css": "tsfont-apple", "hexCodepoint": "0xe816", "codepoint": 59414}, {"name": "application", "css": "tsfont-application", "hexCodepoint": "0xe817", "codepoint": 59415}, {"name": "apps", "css": "tsfont-apps", "hexCodepoint": "0xe818", "codepoint": 59416}, {"name": "arrow-corner-left", "css": "tsfont-arrow-corner-left", "hexCodepoint": "0xe819", "codepoint": 59417}, {"name": "arrow-corner-right", "css": "tsfont-arrow-corner-right", "hexCodepoint": "0xe81a", "codepoint": 59418}, {"name": "arrow-down", "css": "tsfont-arrow-down", "hexCodepoint": "0xe81b", "codepoint": 59419}, {"name": "arrow-left", "css": "tsfont-arrow-left", "hexCodepoint": "0xe81c", "codepoint": 59420}, {"name": "arrow-right", "css": "tsfont-arrow-right", "hexCodepoint": "0xe81d", "codepoint": 59421}, {"name": "arrow-up", "css": "tsfont-arrow-up", "hexCodepoint": "0xe81e", "codepoint": 59422}, {"name": "attachment", "css": "tsfont-attachment", "hexCodepoint": "0xe81f", "codepoint": 59423}, {"name": "attribute", "css": "tsfont-attribute", "hexCodepoint": "0xe820", "codepoint": 59424}, {"name": "auth", "css": "tsfont-auth", "hexCodepoint": "0xe821", "codepoint": 59425}, {"name": "auto", "css": "tsfont-auto", "hexCodepoint": "0xe822", "codepoint": 59426}, {"name": "aws", "css": "tsfont-aws", "hexCodepoint": "0xe823", "codepoint": 59427}, {"name": "bad", "css": "tsfont-bad", "hexCodepoint": "0xe824", "codepoint": 59428}, {"name": "badge", "css": "tsfont-badge", "hexCodepoint": "0xe825", "codepoint": 59429}, {"name": "bao<PERSON>o", "css": "tsfont-baobiao", "hexCodepoint": "0xe826", "codepoint": 59430}, {"name": "baosong", "css": "tsfont-baosong", "hexCodepoint": "0xe827", "codepoint": 59431}, {"name": "bar", "css": "tsfont-bar", "hexCodepoint": "0xe828", "codepoint": 59432}, {"name": "barlist", "css": "tsfont-barlist", "hexCodepoint": "0xe829", "codepoint": 59433}, {"name": "batch-download", "css": "tsfont-batch-download", "hexCodepoint": "0xe82a", "codepoint": 59434}, {"name": "batch-success", "css": "tsfont-batch-success", "hexCodepoint": "0xe82b", "codepoint": 59435}, {"name": "batch-upload", "css": "tsfont-batch-upload", "hexCodepoint": "0xe82c", "codepoint": 59436}, {"name": "bell-o", "css": "tsfont-bell-o", "hexCodepoint": "0xe82d", "codepoint": 59437}, {"name": "bell-off", "css": "tsfont-bell-off", "hexCodepoint": "0xe82e", "codepoint": 59438}, {"name": "bell-on", "css": "tsfont-bell-on", "hexCodepoint": "0xe82f", "codepoint": 59439}, {"name": "bell-z", "css": "tsfont-bell-z", "hexCodepoint": "0xe830", "codepoint": 59440}, {"name": "bell", "css": "tsfont-bell", "hexCodepoint": "0xe831", "codepoint": 59441}, {"name": "<PERSON><PERSON><PERSON>", "css": "tsfont-bi<PERSON><PERSON>", "hexCodepoint": "0xe832", "codepoint": 59442}, {"name": "bind", "css": "tsfont-bind", "hexCodepoint": "0xe833", "codepoint": 59443}, {"name": "block", "css": "tsfont-block", "hexCodepoint": "0xe834", "codepoint": 59444}, {"name": "blocklist", "css": "tsfont-blocklist", "hexCodepoint": "0xe835", "codepoint": 59445}, {"name": "blocks", "css": "tsfont-blocks", "hexCodepoint": "0xe836", "codepoint": 59446}, {"name": "bofenfuyongshebei", "css": "tsfont-bofenfuyongshebei", "hexCodepoint": "0xe837", "codepoint": 59447}, {"name": "bold", "css": "tsfont-bold", "hexCodepoint": "0xe838", "codepoint": 59448}, {"name": "book", "css": "tsfont-book", "hexCodepoint": "0xe839", "codepoint": 59449}, {"name": "border-all", "css": "tsfont-border-all", "hexCodepoint": "0xe83a", "codepoint": 59450}, {"name": "border-clear", "css": "tsfont-border-clear", "hexCodepoint": "0xe83b", "codepoint": 59451}, {"name": "broom", "css": "tsfont-broom", "hexCodepoint": "0xe83c", "codepoint": 59452}, {"name": "browser", "css": "tsfont-browser", "hexCodepoint": "0xe83d", "codepoint": 59453}, {"name": "bushushuxing", "css": "tsfont-bushushuxing", "hexCodepoint": "0xe83e", "codepoint": 59454}, {"name": "calendar", "css": "tsfont-calendar", "hexCodepoint": "0xe83f", "codepoint": 59455}, {"name": "callcenter", "css": "tsfont-callcenter", "hexCodepoint": "0xe840", "codepoint": 59456}, {"name": "canvas", "css": "tsfont-canvas", "hexCodepoint": "0xe841", "codepoint": 59457}, {"name": "celve", "css": "tsfont-celve", "hexCodepoint": "0xe842", "codepoint": 59458}, {"name": "center", "css": "tsfont-center", "hexCodepoint": "0xe843", "codepoint": 59459}, {"name": "centos", "css": "tsfont-centos", "hexCodepoint": "0xe844", "codepoint": 59460}, {"name": "certificate", "css": "tsfont-certificate", "hexCodepoint": "0xe845", "codepoint": 59461}, {"name": "change", "css": "tsfont-change", "hexCodepoint": "0xe846", "codepoint": 59462}, {"name": "changing", "css": "tsfont-changing", "hexCodepoint": "0xe847", "codepoint": 59463}, {"name": "chart-area", "css": "tsfont-chart-area", "hexCodepoint": "0xe848", "codepoint": 59464}, {"name": "chart-bar", "css": "tsfont-chart-bar", "hexCodepoint": "0xe849", "codepoint": 59465}, {"name": "chart-bullet", "css": "tsfont-chart-bullet", "hexCodepoint": "0xe84a", "codepoint": 59466}, {"name": "chart-funnel", "css": "tsfont-chart-funnel", "hexCodepoint": "0xe84b", "codepoint": 59467}, {"name": "chart-gauge", "css": "tsfont-chart-gauge", "hexCodepoint": "0xe84c", "codepoint": 59468}, {"name": "chart-groupedcolumn", "css": "tsfont-chart-groupedcolumn", "hexCodepoint": "0xe84d", "codepoint": 59469}, {"name": "chart-heatmap", "css": "tsfont-chart-heatmap", "hexCodepoint": "0xe84e", "codepoint": 59470}, {"name": "chart-line", "css": "tsfont-chart-line", "hexCodepoint": "0xe84f", "codepoint": 59471}, {"name": "chart-lines", "css": "tsfont-chart-lines", "hexCodepoint": "0xe850", "codepoint": 59472}, {"name": "chart-liquid", "css": "tsfont-chart-liquid", "hexCodepoint": "0xe851", "codepoint": 59473}, {"name": "chart-number", "css": "tsfont-chart-number", "hexCodepoint": "0xe852", "codepoint": 59474}, {"name": "chart-pie", "css": "tsfont-chart-pie", "hexCodepoint": "0xe853", "codepoint": 59475}, {"name": "chart-polyline", "css": "tsfont-chart-polyline", "hexCodepoint": "0xe854", "codepoint": 59476}, {"name": "chart-progress", "css": "tsfont-chart-progress", "hexCodepoint": "0xe855", "codepoint": 59477}, {"name": "chart-radar", "css": "tsfont-chart-radar", "hexCodepoint": "0xe856", "codepoint": 59478}, {"name": "chart-rose", "css": "tsfont-chart-rose", "hexCodepoint": "0xe857", "codepoint": 59479}, {"name": "chart-sankey", "css": "tsfont-chart-sankey", "hexCodepoint": "0xe858", "codepoint": 59480}, {"name": "chart-scatter", "css": "tsfont-chart-scatter", "hexCodepoint": "0xe859", "codepoint": 59481}, {"name": "chart-scatterbubble", "css": "tsfont-chart-scatterbubble", "hexCodepoint": "0xe85a", "codepoint": 59482}, {"name": "chart-stacked<PERSON><PERSON>n", "css": "tsfont-chart-stackedcolumn", "hexCodepoint": "0xe85b", "codepoint": 59483}, {"name": "chart-table", "css": "tsfont-chart-table", "hexCodepoint": "0xe85c", "codepoint": 59484}, {"name": "chart-wordcloud", "css": "tsfont-chart-wordcloud", "hexCodepoint": "0xe85e", "codepoint": 59486}, {"name": "check-o", "css": "tsfont-check-o", "hexCodepoint": "0xe85f", "codepoint": 59487}, {"name": "check-s", "css": "tsfont-check-s", "hexCodepoint": "0xe860", "codepoint": 59488}, {"name": "check-some", "css": "tsfont-check-some", "hexCodepoint": "0xe861", "codepoint": 59489}, {"name": "check-square", "css": "tsfont-check-square", "hexCodepoint": "0xe862", "codepoint": 59490}, {"name": "check", "css": "tsfont-check", "hexCodepoint": "0xe863", "codepoint": 59491}, {"name": "checklist", "css": "tsfont-checklist", "hexCodepoint": "0xe864", "codepoint": 59492}, {"name": "ci-o", "css": "tsfont-ci-o", "hexCodepoint": "0xe865", "codepoint": 59493}, {"name": "ci", "css": "tsfont-ci", "hexCodepoint": "0xe866", "codepoint": 59494}, {"name": "cidaiku", "css": "tsfont-cidaiku", "hexCodepoint": "0xe867", "codepoint": 59495}, {"name": "cidaizhenlie", "css": "tsfont-cidaizhenlie", "hexCodepoint": "0xe868", "codepoint": 59496}, {"name": "cientityselect", "css": "tsfont-cientityselect", "hexCodepoint": "0xe869", "codepoint": 59497}, {"name": "circle-o", "css": "tsfont-circle-o", "hexCodepoint": "0xe86a", "codepoint": 59498}, {"name": "circle", "css": "tsfont-circle", "hexCodepoint": "0xe86b", "codepoint": 59499}, {"name": "circulation-o", "css": "tsfont-circulation-o", "hexCodepoint": "0xe86c", "codepoint": 59500}, {"name": "circulation-s", "css": "tsfont-circulation-s", "hexCodepoint": "0xe86d", "codepoint": 59501}, {"name": "cisco", "css": "tsfont-cisco", "hexCodepoint": "0xe86e", "codepoint": 59502}, {"name": "close-o", "css": "tsfont-close-o", "hexCodepoint": "0xe86f", "codepoint": 59503}, {"name": "close-s", "css": "tsfont-close-s", "hexCodepoint": "0xe870", "codepoint": 59504}, {"name": "close", "css": "tsfont-close", "hexCodepoint": "0xe871", "codepoint": 59505}, {"name": "cloud", "css": "tsfont-cloud", "hexCodepoint": "0xe872", "codepoint": 59506}, {"name": "cluster-mode", "css": "tsfont-cluster-mode", "hexCodepoint": "0xe873", "codepoint": 59507}, {"name": "cluster-software", "css": "tsfont-cluster-software", "hexCodepoint": "0xe874", "codepoint": 59508}, {"name": "cluster", "css": "tsfont-cluster", "hexCodepoint": "0xe875", "codepoint": 59509}, {"name": "code", "css": "tsfont-code", "hexCodepoint": "0xe876", "codepoint": 59510}, {"name": "collapse", "css": "tsfont-collapse", "hexCodepoint": "0xe877", "codepoint": 59511}, {"name": "common", "css": "tsfont-common", "hexCodepoint": "0xe878", "codepoint": 59512}, {"name": "compare", "css": "tsfont-compare", "hexCodepoint": "0xe879", "codepoint": 59513}, {"name": "component", "css": "tsfont-component", "hexCodepoint": "0xe87a", "codepoint": 59514}, {"name": "config", "css": "tsfont-config", "hexCodepoint": "0xe87b", "codepoint": 59515}, {"name": "connector", "css": "tsfont-connector", "hexCodepoint": "0xe87c", "codepoint": 59516}, {"name": "console", "css": "tsfont-console", "hexCodepoint": "0xe87d", "codepoint": 59517}, {"name": "cooperate-o", "css": "tsfont-cooperate-o", "hexCodepoint": "0xe87e", "codepoint": 59518}, {"name": "cooperate-s", "css": "tsfont-cooperate-s", "hexCodepoint": "0xe87f", "codepoint": 59519}, {"name": "copy", "css": "tsfont-copy", "hexCodepoint": "0xe880", "codepoint": 59520}, {"name": "danger-level", "css": "tsfont-danger-level", "hexCodepoint": "0xe881", "codepoint": 59521}, {"name": "danger-o", "css": "tsfont-danger-o", "hexCodepoint": "0xe882", "codepoint": 59522}, {"name": "danger-s", "css": "tsfont-danger-s", "hexCodepoint": "0xe883", "codepoint": 59523}, {"name": "datacenter", "css": "tsfont-datacenter", "hexCodepoint": "0xe884", "codepoint": 59524}, {"name": "day", "css": "tsfont-day", "hexCodepoint": "0xe885", "codepoint": 59525}, {"name": "db-cluster", "css": "tsfont-db-cluster", "hexCodepoint": "0xe886", "codepoint": 59526}, {"name": "db-ins", "css": "tsfont-db-ins", "hexCodepoint": "0xe887", "codepoint": 59527}, {"name": "db", "css": "tsfont-db", "hexCodepoint": "0xe888", "codepoint": 59528}, {"name": "db2", "css": "tsfont-db2", "hexCodepoint": "0xe889", "codepoint": 59529}, {"name": "debug", "css": "tsfont-debug", "hexCodepoint": "0xe88a", "codepoint": 59530}, {"name": "dell", "css": "tsfont-dell", "hexCodepoint": "0xe88b", "codepoint": 59531}, {"name": "desktop", "css": "tsfont-desktop", "hexCodepoint": "0xe88c", "codepoint": 59532}, {"name": "devices", "css": "tsfont-devices", "hexCodepoint": "0xe88d", "codepoint": 59533}, {"name": "dictionary", "css": "tsfont-dictionary", "hexCodepoint": "0xe88e", "codepoint": 59534}, {"name": "dns", "css": "tsfont-dns", "hexCodepoint": "0xe88f", "codepoint": 59535}, {"name": "docker", "css": "tsfont-docker", "hexCodepoint": "0xe890", "codepoint": 59536}, {"name": "dot", "css": "tsfont-dot", "hexCodepoint": "0xe891", "codepoint": 59537}, {"name": "down", "css": "tsfont-down", "hexCodepoint": "0xe892", "codepoint": 59538}, {"name": "download", "css": "tsfont-download", "hexCodepoint": "0xe893", "codepoint": 59539}, {"name": "drafts", "css": "tsfont-drafts", "hexCodepoint": "0xe894", "codepoint": 59540}, {"name": "drag", "css": "tsfont-drag", "hexCodepoint": "0xe895", "codepoint": 59541}, {"name": "drop-down-s", "css": "tsfont-drop-down-s", "hexCodepoint": "0xe896", "codepoint": 59542}, {"name": "drop-down", "css": "tsfont-drop-down", "hexCodepoint": "0xe897", "codepoint": 59543}, {"name": "drop-left", "css": "tsfont-drop-left", "hexCodepoint": "0xe898", "codepoint": 59544}, {"name": "drop-right", "css": "tsfont-drop-right", "hexCodepoint": "0xe899", "codepoint": 59545}, {"name": "drop-up", "css": "tsfont-drop-up", "hexCodepoint": "0xe89a", "codepoint": 59546}, {"name": "<PERSON><PERSON><PERSON>g<PERSON><PERSON><PERSON>", "css": "tsfont-duixiangcunchu", "hexCodepoint": "0xe89b", "codepoint": 59547}, {"name": "edit-s", "css": "tsfont-edit-s", "hexCodepoint": "0xe89c", "codepoint": 59548}, {"name": "edit", "css": "tsfont-edit", "hexCodepoint": "0xe89d", "codepoint": 59549}, {"name": "elasticsearch", "css": "tsfont-elasticsearch", "hexCodepoint": "0xe89e", "codepoint": 59550}, {"name": "empty", "css": "tsfont-empty", "hexCodepoint": "0xe89f", "codepoint": 59551}, {"name": "eoa", "css": "tsfont-eoa", "hexCodepoint": "0xe8a0", "codepoint": 59552}, {"name": "excellent", "css": "tsfont-excellent", "hexCodepoint": "0xe8a1", "codepoint": 59553}, {"name": "expand", "css": "tsfont-expand", "hexCodepoint": "0xe8a2", "codepoint": 59554}, {"name": "export-s", "css": "tsfont-export-s", "hexCodepoint": "0xe8a3", "codepoint": 59555}, {"name": "export", "css": "tsfont-export", "hexCodepoint": "0xe8a4", "codepoint": 59556}, {"name": "eye-off", "css": "tsfont-eye-off", "hexCodepoint": "0xe8a5", "codepoint": 59557}, {"name": "eye", "css": "tsfont-eye", "hexCodepoint": "0xe8a6", "codepoint": 59558}, {"name": "f5", "css": "tsfont-f5", "hexCodepoint": "0xe8a7", "codepoint": 59559}, {"name": "fangbingduwangguan", "css": "tsfont-fangbingduwangguan", "hexCodepoint": "0xe8a8", "codepoint": 59560}, {"name": "fcdev", "css": "tsfont-fcdev", "hexCodepoint": "0xe8a9", "codepoint": 59561}, {"name": "fcswitch", "css": "tsfont-fcswitch", "hexCodepoint": "0xe8aa", "codepoint": 59562}, {"name": "file-multi", "css": "tsfont-file-multi", "hexCodepoint": "0xe8ab", "codepoint": 59563}, {"name": "file-single", "css": "tsfont-file-single", "hexCodepoint": "0xe8ac", "codepoint": 59564}, {"name": "filter-o", "css": "tsfont-filter-o", "hexCodepoint": "0xe8ad", "codepoint": 59565}, {"name": "filter", "css": "tsfont-filter", "hexCodepoint": "0xe8ae", "codepoint": 59566}, {"name": "firewall", "css": "tsfont-firewall", "hexCodepoint": "0xe8af", "codepoint": 59567}, {"name": "first", "css": "tsfont-first", "hexCodepoint": "0xe8b0", "codepoint": 59568}, {"name": "flow-children", "css": "tsfont-flow-children", "hexCodepoint": "0xe8b1", "codepoint": 59569}, {"name": "flow-siblings", "css": "tsfont-flow-siblings", "hexCodepoint": "0xe8b2", "codepoint": 59570}, {"name": "flow", "css": "tsfont-flow", "hexCodepoint": "0xe8b3", "codepoint": 59571}, {"name": "fold", "css": "tsfont-fold", "hexCodepoint": "0xe8b4", "codepoint": 59572}, {"name": "folder-o", "css": "tsfont-folder-o", "hexCodepoint": "0xe8b5", "codepoint": 59573}, {"name": "folder-s", "css": "tsfont-folder-s", "hexCodepoint": "0xe8b6", "codepoint": 59574}, {"name": "folder-share", "css": "tsfont-folder-share", "hexCodepoint": "0xe8b7", "codepoint": 59575}, {"name": "font-size", "css": "tsfont-font-size", "hexCodepoint": "0xe8b8", "codepoint": 59576}, {"name": "forbid", "css": "tsfont-forbid", "hexCodepoint": "0xe8b9", "codepoint": 59577}, {"name": "formcascadelist", "css": "tsfont-formcascadelist", "hexCodepoint": "0xe8ba", "codepoint": 59578}, {"name": "formdynamiclist", "css": "tsfont-formdynamiclist", "hexCodepoint": "0xe8bb", "codepoint": 59579}, {"name": "forminput", "css": "tsfont-forminput", "hexCodepoint": "0xe8bc", "codepoint": 59580}, {"name": "formlink", "css": "tsfont-formlink", "hexCodepoint": "0xe8bd", "codepoint": 59581}, {"name": "formlist", "css": "tsfont-formlist", "hexCodepoint": "0xe8be", "codepoint": 59582}, {"name": "formselect", "css": "tsfont-formselect", "hexCodepoint": "0xe8bf", "codepoint": 59583}, {"name": "formselectcascader", "css": "tsfont-formselectcascader", "hexCodepoint": "0xe8c0", "codepoint": 59584}, {"name": "formstaticlist", "css": "tsfont-formstaticlist", "hexCodepoint": "0xe8c1", "codepoint": 59585}, {"name": "formtextarea", "css": "tsfont-formtextarea", "hexCodepoint": "0xe8c2", "codepoint": 59586}, {"name": "formtime", "css": "tsfont-formtime", "hexCodepoint": "0xe8c3", "codepoint": 59587}, {"name": "freebsd", "css": "tsfont-freebsd", "hexCodepoint": "0xe8c4", "codepoint": 59588}, {"name": "fullscreen", "css": "tsfont-fullscreen", "hexCodepoint": "0xe8c5", "codepoint": 59589}, {"name": "f<PERSON><PERSON><PERSON>", "css": "tsfont-f<PERSON><PERSON><PERSON>", "hexCodepoint": "0xe8c6", "codepoint": 59590}, {"name": "good", "css": "tsfont-good", "hexCodepoint": "0xe8c7", "codepoint": 59591}, {"name": "group", "css": "tsfont-group", "hexCodepoint": "0xe8c8", "codepoint": 59592}, {"name": "hadoop", "css": "tsfont-hadoop", "hexCodepoint": "0xe8c9", "codepoint": 59593}, {"name": "hand", "css": "tsfont-hand", "hexCodepoint": "0xe8ca", "codepoint": 59594}, {"name": "hardware", "css": "tsfont-hardware", "hexCodepoint": "0xe8cb", "codepoint": 59595}, {"name": "heart-s", "css": "tsfont-heart-s", "hexCodepoint": "0xe8cc", "codepoint": 59596}, {"name": "history", "css": "tsfont-history", "hexCodepoint": "0xe8cd", "codepoint": 59597}, {"name": "home", "css": "tsfont-home", "hexCodepoint": "0xe8ce", "codepoint": 59598}, {"name": "horizontal-center", "css": "tsfont-horizontal-center", "hexCodepoint": "0xe8cf", "codepoint": 59599}, {"name": "horizontal-justify", "css": "tsfont-horizontal-justify", "hexCodepoint": "0xe8d0", "codepoint": 59600}, {"name": "horizontal-left", "css": "tsfont-horizontal-left", "hexCodepoint": "0xe8d1", "codepoint": 59601}, {"name": "horizontal-right", "css": "tsfont-horizontal-right", "hexCodepoint": "0xe8d2", "codepoint": 59602}, {"name": "host", "css": "tsfont-host", "hexCodepoint": "0xe8d3", "codepoint": 59603}, {"name": "hp", "css": "tsfont-hp", "hexCodepoint": "0xe8d4", "codepoint": 59604}, {"name": "hua<PERSON>", "css": "tsfont-huawei", "hexCodepoint": "0xe8d5", "codepoint": 59605}, {"name": "ibm", "css": "tsfont-ibm", "hexCodepoint": "0xe8d6", "codepoint": 59606}, {"name": "iis", "css": "tsfont-iis", "hexCodepoint": "0xe8d7", "codepoint": 59607}, {"name": "image-s", "css": "tsfont-image-s", "hexCodepoint": "0xe8d8", "codepoint": 59608}, {"name": "image", "css": "tsfont-image", "hexCodepoint": "0xe8d9", "codepoint": 59609}, {"name": "img-center", "css": "tsfont-img-center", "hexCodepoint": "0xe8da", "codepoint": 59610}, {"name": "img-left", "css": "tsfont-img-left", "hexCodepoint": "0xe8db", "codepoint": 59611}, {"name": "img-right", "css": "tsfont-img-right", "hexCodepoint": "0xe8dc", "codepoint": 59612}, {"name": "import-s", "css": "tsfont-import-s", "hexCodepoint": "0xe8dd", "codepoint": 59613}, {"name": "import", "css": "tsfont-import", "hexCodepoint": "0xe8de", "codepoint": 59614}, {"name": "indent", "css": "tsfont-indent", "hexCodepoint": "0xe8df", "codepoint": 59615}, {"name": "info-o", "css": "tsfont-info-o", "hexCodepoint": "0xe8e0", "codepoint": 59616}, {"name": "info-s", "css": "tsfont-info-s", "hexCodepoint": "0xe8e1", "codepoint": 59617}, {"name": "info", "css": "tsfont-info", "hexCodepoint": "0xe8e2", "codepoint": 59618}, {"name": "informix", "css": "tsfont-informix", "hexCodepoint": "0xe8e3", "codepoint": 59619}, {"name": "inlink", "css": "tsfont-inlink", "hexCodepoint": "0xe8e4", "codepoint": 59620}, {"name": "ins", "css": "tsfont-ins", "hexCodepoint": "0xe8e5", "codepoint": 59621}, {"name": "inspection", "css": "tsfont-inspection", "hexCodepoint": "0xe8e6", "codepoint": 59622}, {"name": "inspur", "css": "tsfont-inspur", "hexCodepoint": "0xe8e7", "codepoint": 59623}, {"name": "instance", "css": "tsfont-instance", "hexCodepoint": "0xe8e8", "codepoint": 59624}, {"name": "integration", "css": "tsfont-integration", "hexCodepoint": "0xe8e9", "codepoint": 59625}, {"name": "internet", "css": "tsfont-internet", "hexCodepoint": "0xe8ea", "codepoint": 59626}, {"name": "ip-list", "css": "tsfont-ip-list", "hexCodepoint": "0xe8eb", "codepoint": 59627}, {"name": "ip-object", "css": "tsfont-ip-object", "hexCodepoint": "0xe8ec", "codepoint": 59628}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "css": "tsfont-ipliebiao", "hexCodepoint": "0xe8ed", "codepoint": 59629}, {"name": "italic", "css": "tsfont-italic", "hexCodepoint": "0xe8ee", "codepoint": 59630}, {"name": "ITfuwu", "css": "tsfont-ITfuwu", "hexCodepoint": "0xe8ef", "codepoint": 59631}, {"name": "java", "css": "tsfont-java", "hexCodepoint": "0xe8f0", "codepoint": 59632}, {"name": "j<PERSON>s", "css": "tsfont-jboss", "hexCodepoint": "0xe8f1", "codepoint": 59633}, {"name": "jetty", "css": "tsfont-jetty", "hexCodepoint": "0xe8f2", "codepoint": 59634}, {"name": "jiamishebei", "css": "tsfont-jiamishebei", "hexCodepoint": "0xe8f3", "codepoint": 59635}, {"name": "jicheng", "css": "tsfont-jicheng", "hexCodepoint": "0xe8f4", "codepoint": 59636}, {"name": "jifang", "css": "tsfont-jifang", "hexCodepoint": "0xe8f5", "codepoint": 59637}, {"name": "json", "css": "tsfont-json", "hexCodepoint": "0xe8f6", "codepoint": 59638}, {"name": "juniper", "css": "tsfont-juniper", "hexCodepoint": "0xe8f7", "codepoint": 59639}, {"name": "k8s", "css": "tsfont-k8s", "hexCodepoint": "0xe8f8", "codepoint": 59640}, {"name": "kafka", "css": "tsfont-kafka", "hexCodepoint": "0xe8f9", "codepoint": 59641}, {"name": "kang<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "css": "tsfont-kang<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hexCodepoint": "0xe8fa", "codepoint": 59642}, {"name": "keepalive", "css": "tsfont-keepalive", "hexCodepoint": "0xe8fb", "codepoint": 59643}, {"name": "label-s", "css": "tsfont-label-s", "hexCodepoint": "0xe8fc", "codepoint": 59644}, {"name": "label", "css": "tsfont-label", "hexCodepoint": "0xe8fd", "codepoint": 59645}, {"name": "last", "css": "tsfont-last", "hexCodepoint": "0xe8fe", "codepoint": 59646}, {"name": "layer", "css": "tsfont-layer", "hexCodepoint": "0xe8ff", "codepoint": 59647}, {"name": "left", "css": "tsfont-left", "hexCodepoint": "0xe900", "codepoint": 59648}, {"name": "lightning", "css": "tsfont-lightning", "hexCodepoint": "0xe901", "codepoint": 59649}, {"name": "lighttpd", "css": "tsfont-lighttpd", "hexCodepoint": "0xe902", "codepoint": 59650}, {"name": "linux", "css": "tsfont-linux", "hexCodepoint": "0xe903", "codepoint": 59651}, {"name": "list", "css": "tsfont-list", "hexCodepoint": "0xe904", "codepoint": 59652}, {"name": "liuliangfenxishebei", "css": "tsfont-liuliangfenxishebei", "hexCodepoint": "0xe905", "codepoint": 59653}, {"name": "loadblance-vs", "css": "tsfont-loadblance-vs", "hexCodepoint": "0xe906", "codepoint": 59654}, {"name": "location-o", "css": "tsfont-location-o", "hexCodepoint": "0xe907", "codepoint": 59655}, {"name": "location", "css": "tsfont-location", "hexCodepoint": "0xe908", "codepoint": 59656}, {"name": "lock-s", "css": "tsfont-lock-s", "hexCodepoint": "0xe909", "codepoint": 59657}, {"name": "lock", "css": "tsfont-lock", "hexCodepoint": "0xe90a", "codepoint": 59658}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "css": "tsfont-<PERSON><PERSON><PERSON><PERSON><PERSON>", "hexCodepoint": "0xe90b", "codepoint": 59659}, {"name": "lun", "css": "tsfont-lun", "hexCodepoint": "0xe90c", "codepoint": 59660}, {"name": "mail-o", "css": "tsfont-mail-o", "hexCodepoint": "0xe90d", "codepoint": 59661}, {"name": "mail-read-o", "css": "tsfont-mail-read-o", "hexCodepoint": "0xe90e", "codepoint": 59662}, {"name": "mail-s", "css": "tsfont-mail-s", "hexCodepoint": "0xe90f", "codepoint": 59663}, {"name": "mail-unread-o", "css": "tsfont-mail-unread-o", "hexCodepoint": "0xe910", "codepoint": 59664}, {"name": "manager", "css": "tsfont-manager", "hexCodepoint": "0xe911", "codepoint": 59665}, {"name": "mark-all", "css": "tsfont-mark-all", "hexCodepoint": "0xe912", "codepoint": 59666}, {"name": "memcached", "css": "tsfont-memcached", "hexCodepoint": "0xe913", "codepoint": 59667}, {"name": "mesos", "css": "tsfont-mesos", "hexCodepoint": "0xe914", "codepoint": 59668}, {"name": "message-o", "css": "tsfont-message-o", "hexCodepoint": "0xe915", "codepoint": 59669}, {"name": "message", "css": "tsfont-message", "hexCodepoint": "0xe916", "codepoint": 59670}, {"name": "minus-o", "css": "tsfont-minus-o", "hexCodepoint": "0xe917", "codepoint": 59671}, {"name": "minus-s", "css": "tsfont-minus-s", "hexCodepoint": "0xe918", "codepoint": 59672}, {"name": "minus-square", "css": "tsfont-minus-square", "hexCodepoint": "0xe919", "codepoint": 59673}, {"name": "minus", "css": "tsfont-minus", "hexCodepoint": "0xe91a", "codepoint": 59674}, {"name": "mm-bat", "css": "tsfont-mm-bat", "hexCodepoint": "0xe91b", "codepoint": 59675}, {"name": "mm-bmp", "css": "tsfont-mm-bmp", "hexCodepoint": "0xe91c", "codepoint": 59676}, {"name": "mm-cls", "css": "tsfont-mm-cls", "hexCodepoint": "0xe91d", "codepoint": 59677}, {"name": "mm-cmd", "css": "tsfont-mm-cmd", "hexCodepoint": "0xe91e", "codepoint": 59678}, {"name": "mm-cnf", "css": "tsfont-mm-cnf", "hexCodepoint": "0xe91f", "codepoint": 59679}, {"name": "mm-css", "css": "tsfont-mm-css", "hexCodepoint": "0xe920", "codepoint": 59680}, {"name": "mm-dir", "css": "tsfont-mm-dir", "hexCodepoint": "0xe921", "codepoint": 59681}, {"name": "mm-doc", "css": "tsfont-mm-doc", "hexCodepoint": "0xe922", "codepoint": 59682}, {"name": "mm-docx", "css": "tsfont-mm-docx", "hexCodepoint": "0xe923", "codepoint": 59683}, {"name": "mm-exe", "css": "tsfont-mm-exe", "hexCodepoint": "0xe924", "codepoint": 59684}, {"name": "mm-gif", "css": "tsfont-mm-gif", "hexCodepoint": "0xe925", "codepoint": 59685}, {"name": "mm-gzip", "css": "tsfont-mm-gzip", "hexCodepoint": "0xe926", "codepoint": 59686}, {"name": "mm-html", "css": "tsfont-mm-html", "hexCodepoint": "0xe927", "codepoint": 59687}, {"name": "mm-java", "css": "tsfont-mm-java", "hexCodepoint": "0xe928", "codepoint": 59688}, {"name": "mm-jpeg", "css": "tsfont-mm-jpeg", "hexCodepoint": "0xe929", "codepoint": 59689}, {"name": "mm-jpg", "css": "tsfont-mm-jpg", "hexCodepoint": "0xe92a", "codepoint": 59690}, {"name": "mm-js", "css": "tsfont-mm-js", "hexCodepoint": "0xe92b", "codepoint": 59691}, {"name": "mm-misc", "css": "tsfont-mm-misc", "hexCodepoint": "0xe92c", "codepoint": 59692}, {"name": "mm-mov", "css": "tsfont-mm-mov", "hexCodepoint": "0xe92d", "codepoint": 59693}, {"name": "mm-mp4", "css": "tsfont-mm-mp4", "hexCodepoint": "0xe92e", "codepoint": 59694}, {"name": "mm-png", "css": "tsfont-mm-png", "hexCodepoint": "0xe92f", "codepoint": 59695}, {"name": "mm-ppt", "css": "tsfont-mm-ppt", "hexCodepoint": "0xe930", "codepoint": 59696}, {"name": "mm-py", "css": "tsfont-mm-py", "hexCodepoint": "0xe931", "codepoint": 59697}, {"name": "mm-rar", "css": "tsfont-mm-rar", "hexCodepoint": "0xe932", "codepoint": 59698}, {"name": "mm-rpm", "css": "tsfont-mm-rpm", "hexCodepoint": "0xe933", "codepoint": 59699}, {"name": "mm-rtf", "css": "tsfont-mm-rtf", "hexCodepoint": "0xe934", "codepoint": 59700}, {"name": "mm-sh", "css": "tsfont-mm-sh", "hexCodepoint": "0xe935", "codepoint": 59701}, {"name": "mm-sql", "css": "tsfont-mm-sql", "hexCodepoint": "0xe936", "codepoint": 59702}, {"name": "mm-svg", "css": "tsfont-mm-svg", "hexCodepoint": "0xe937", "codepoint": 59703}, {"name": "mm-tar", "css": "tsfont-mm-tar", "hexCodepoint": "0xe938", "codepoint": 59704}, {"name": "mm-txt", "css": "tsfont-mm-txt", "hexCodepoint": "0xe939", "codepoint": 59705}, {"name": "mm-unknown", "css": "tsfont-mm-unknown", "hexCodepoint": "0xe93a", "codepoint": 59706}, {"name": "mm-vbs", "css": "tsfont-mm-vbs", "hexCodepoint": "0xe93b", "codepoint": 59707}, {"name": "mm-xls", "css": "tsfont-mm-xls", "hexCodepoint": "0xe93c", "codepoint": 59708}, {"name": "mm-xlsm", "css": "tsfont-mm-xlsm", "hexCodepoint": "0xe93d", "codepoint": 59709}, {"name": "mm-xlsx", "css": "tsfont-mm-xlsx", "hexCodepoint": "0xe93e", "codepoint": 59710}, {"name": "mm-xml", "css": "tsfont-mm-xml", "hexCodepoint": "0xe93f", "codepoint": 59711}, {"name": "mm-zip", "css": "tsfont-mm-zip", "hexCodepoint": "0xe940", "codepoint": 59712}, {"name": "module", "css": "tsfont-module", "hexCodepoint": "0xe941", "codepoint": 59713}, {"name": "modules", "css": "tsfont-modules", "hexCodepoint": "0xe942", "codepoint": 59714}, {"name": "mongodb", "css": "tsfont-mongodb", "hexCodepoint": "0xe943", "codepoint": 59715}, {"name": "monitor", "css": "tsfont-monitor", "hexCodepoint": "0xe944", "codepoint": 59716}, {"name": "mssqlserver", "css": "tsfont-mssqlserver", "hexCodepoint": "0xe945", "codepoint": 59717}, {"name": "mysql", "css": "tsfont-mysql", "hexCodepoint": "0xe946", "codepoint": 59718}, {"name": "net-area", "css": "tsfont-net-area", "hexCodepoint": "0xe947", "codepoint": 59719}, {"name": "netarea", "css": "tsfont-netarea", "hexCodepoint": "0xe948", "codepoint": 59720}, {"name": "netdev", "css": "tsfont-netdev", "hexCodepoint": "0xe949", "codepoint": 59721}, {"name": "nginx", "css": "tsfont-nginx", "hexCodepoint": "0xe94a", "codepoint": 59722}, {"name": "night", "css": "tsfont-night", "hexCodepoint": "0xe94b", "codepoint": 59723}, {"name": "node", "css": "tsfont-node", "hexCodepoint": "0xe94c", "codepoint": 59724}, {"name": "node1", "css": "tsfont-node1", "hexCodepoint": "0xe94d", "codepoint": 59725}, {"name": "non-auth", "css": "tsfont-non-auth", "hexCodepoint": "0xe94e", "codepoint": 59726}, {"name": "novmtool", "css": "tsfont-novmtool", "hexCodepoint": "0xe94f", "codepoint": 59727}, {"name": "nutanix", "css": "tsfont-nutanix", "hexCodepoint": "0xe950", "codepoint": 59728}, {"name": "off-fullscreen", "css": "tsfont-off-fullscreen", "hexCodepoint": "0xe951", "codepoint": 59729}, {"name": "openstack", "css": "tsfont-openstack", "hexCodepoint": "0xe952", "codepoint": 59730}, {"name": "option-horizontal", "css": "tsfont-option-horizontal", "hexCodepoint": "0xe953", "codepoint": 59731}, {"name": "option-vertical", "css": "tsfont-option-vertical", "hexCodepoint": "0xe954", "codepoint": 59732}, {"name": "oracle-rac", "css": "tsfont-oracle-rac", "hexCodepoint": "0xe955", "codepoint": 59733}, {"name": "oracle", "css": "tsfont-oracle", "hexCodepoint": "0xe956", "codepoint": 59734}, {"name": "orderlist", "css": "tsfont-orderlist", "hexCodepoint": "0xe957", "codepoint": 59735}, {"name": "ordinary", "css": "tsfont-ordinary", "hexCodepoint": "0xe958", "codepoint": 59736}, {"name": "os-cluster", "css": "tsfont-os-cluster", "hexCodepoint": "0xe959", "codepoint": 59737}, {"name": "os", "css": "tsfont-os", "hexCodepoint": "0xe95a", "codepoint": 59738}, {"name": "outdent", "css": "tsfont-outdent", "hexCodepoint": "0xe95b", "codepoint": 59739}, {"name": "outline-s", "css": "tsfont-outline-s", "hexCodepoint": "0xe95c", "codepoint": 59740}, {"name": "palette", "css": "tsfont-palette", "hexCodepoint": "0xe95d", "codepoint": 59741}, {"name": "pause-o", "css": "tsfont-pause-o", "hexCodepoint": "0xe95e", "codepoint": 59742}, {"name": "pause-s", "css": "tsfont-pause-s", "hexCodepoint": "0xe95f", "codepoint": 59743}, {"name": "pause", "css": "tsfont-pause", "hexCodepoint": "0xe960", "codepoint": 59744}, {"name": "p<PERSON>zhi<PERSON><PERSON><PERSON>", "css": "tsfont-peizhiguanli", "hexCodepoint": "0xe961", "codepoint": 59745}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "css": "tsfont-peizhihechashebei", "hexCodepoint": "0xe962", "codepoint": 59746}, {"name": "permission", "css": "tsfont-permission", "hexCodepoint": "0xe963", "codepoint": 59747}, {"name": "phone", "css": "tsfont-phone", "hexCodepoint": "0xe964", "codepoint": 59748}, {"name": "php", "css": "tsfont-php", "hexCodepoint": "0xe965", "codepoint": 59749}, {"name": "pie", "css": "tsfont-pie", "hexCodepoint": "0xe966", "codepoint": 59750}, {"name": "pingbijigui", "css": "tsfont-pingbijigui", "hexCodepoint": "0xe967", "codepoint": 59751}, {"name": "play-o", "css": "tsfont-play-o", "hexCodepoint": "0xe968", "codepoint": 59752}, {"name": "play-s", "css": "tsfont-play-s", "hexCodepoint": "0xe969", "codepoint": 59753}, {"name": "play", "css": "tsfont-play", "hexCodepoint": "0xe96a", "codepoint": 59754}, {"name": "plugin", "css": "tsfont-plugin", "hexCodepoint": "0xe96b", "codepoint": 59755}, {"name": "plus-o", "css": "tsfont-plus-o", "hexCodepoint": "0xe96c", "codepoint": 59756}, {"name": "plus-s", "css": "tsfont-plus-s", "hexCodepoint": "0xe96d", "codepoint": 59757}, {"name": "plus-square", "css": "tsfont-plus-square", "hexCodepoint": "0xe96e", "codepoint": 59758}, {"name": "plus", "css": "tsfont-plus", "hexCodepoint": "0xe96f", "codepoint": 59759}, {"name": "pod", "css": "tsfont-pod", "hexCodepoint": "0xe970", "codepoint": 59760}, {"name": "port", "css": "tsfont-port", "hexCodepoint": "0xe971", "codepoint": 59761}, {"name": "postgresql", "css": "tsfont-postgresql", "hexCodepoint": "0xe972", "codepoint": 59762}, {"name": "proxy", "css": "tsfont-proxy", "hexCodepoint": "0xe973", "codepoint": 59763}, {"name": "pulse", "css": "tsfont-pulse", "hexCodepoint": "0xe974", "codepoint": 59764}, {"name": "putongjigu<PERSON>", "css": "tsfont-putongjigui", "hexCodepoint": "0xe975", "codepoint": 59765}, {"name": "python", "css": "tsfont-python", "hexCodepoint": "0xe976", "codepoint": 59766}, {"name": "question-o", "css": "tsfont-question-o", "hexCodepoint": "0xe977", "codepoint": 59767}, {"name": "question-s", "css": "tsfont-question-s", "hexCodepoint": "0xe978", "codepoint": 59768}, {"name": "question", "css": "tsfont-question", "hexCodepoint": "0xe979", "codepoint": 59769}, {"name": "rabbitmq", "css": "tsfont-rabbitmq", "hexCodepoint": "0xe97a", "codepoint": 59770}, {"name": "redhat", "css": "tsfont-redhat", "hexCodepoint": "0xe97b", "codepoint": 59771}, {"name": "redis", "css": "tsfont-redis", "hexCodepoint": "0xe97c", "codepoint": 59772}, {"name": "refresh", "css": "tsfont-refresh", "hexCodepoint": "0xe97d", "codepoint": 59773}, {"name": "reminder", "css": "tsfont-reminder", "hexCodepoint": "0xe97e", "codepoint": 59774}, {"name": "reply-all", "css": "tsfont-reply-all", "hexCodepoint": "0xe97f", "codepoint": 59775}, {"name": "reply", "css": "tsfont-reply", "hexCodepoint": "0xe980", "codepoint": 59776}, {"name": "report", "css": "tsfont-report", "hexCodepoint": "0xe981", "codepoint": 59777}, {"name": "resin", "css": "tsfont-resin", "hexCodepoint": "0xe982", "codepoint": 59778}, {"name": "restart", "css": "tsfont-restart", "hexCodepoint": "0xe983", "codepoint": 59779}, {"name": "restful", "css": "tsfont-restful", "hexCodepoint": "0xe984", "codepoint": 59780}, {"name": "revover", "css": "tsfont-revover", "hexCodepoint": "0xe985", "codepoint": 59781}, {"name": "right", "css": "tsfont-right", "hexCodepoint": "0xe986", "codepoint": 59782}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "css": "tsfont-<PERSON><PERSON><PERSON><PERSON><PERSON>", "hexCodepoint": "0xe987", "codepoint": 59783}, {"name": "rizhishoujiyufenxixitong", "css": "tsfont-rizhishoujiyufenxixitong", "hexCodepoint": "0xe988", "codepoint": 59784}, {"name": "role-s", "css": "tsfont-role-s", "hexCodepoint": "0xe989", "codepoint": 59785}, {"name": "role", "css": "tsfont-role", "hexCodepoint": "0xe98a", "codepoint": 59786}, {"name": "rotate-right", "css": "tsfont-rotate-right", "hexCodepoint": "0xe98b", "codepoint": 59787}, {"name": "router", "css": "tsfont-router", "hexCodepoint": "0xe98c", "codepoint": 59788}, {"name": "run", "css": "tsfont-run", "hexCodepoint": "0xe98d", "codepoint": 59789}, {"name": "r<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "css": "tsfont-ruqinjianceyufangyushebei", "hexCodepoint": "0xe98e", "codepoint": 59790}, {"name": "save", "css": "tsfont-save", "hexCodepoint": "0xe98f", "codepoint": 59791}, {"name": "scene", "css": "tsfont-scene", "hexCodepoint": "0xe990", "codepoint": 59792}, {"name": "script", "css": "tsfont-script", "hexCodepoint": "0xe991", "codepoint": 59793}, {"name": "search-minus", "css": "tsfont-search-minus", "hexCodepoint": "0xe992", "codepoint": 59794}, {"name": "search-plus", "css": "tsfont-search-plus", "hexCodepoint": "0xe993", "codepoint": 59795}, {"name": "search", "css": "tsfont-search", "hexCodepoint": "0xe994", "codepoint": 59796}, {"name": "secdev", "css": "tsfont-secdev", "hexCodepoint": "0xe995", "codepoint": 59797}, {"name": "security", "css": "tsfont-security", "hexCodepoint": "0xe996", "codepoint": 59798}, {"name": "send", "css": "tsfont-send", "hexCodepoint": "0xe997", "codepoint": 59799}, {"name": "setting", "css": "tsfont-setting", "hexCodepoint": "0xe998", "codepoint": 59800}, {"name": "shangwanghangweiguanlishebei", "css": "tsfont-shangwanghangweiguanlishebei", "hexCodepoint": "0xe999", "codepoint": 59801}, {"name": "share", "css": "tsfont-share", "hexCodepoint": "0xe99a", "codepoint": 59802}, {"name": "shitu", "css": "tsfont-shitu", "hexCodepoint": "0xe99b", "codepoint": 59803}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "css": "tsfont-s<PERSON><PERSON><PERSON><PERSON><PERSON>n", "hexCodepoint": "0xe99c", "codepoint": 59804}, {"name": "shunt", "css": "tsfont-shunt", "hexCodepoint": "0xe99d", "codepoint": 59805}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "css": "tsfont-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hexCodepoint": "0xe99e", "codepoint": 59806}, {"name": "sla", "css": "tsfont-sla", "hexCodepoint": "0xe99f", "codepoint": 59807}, {"name": "softwareservice", "css": "tsfont-softwareservice", "hexCodepoint": "0xe9a0", "codepoint": 59808}, {"name": "sop", "css": "tsfont-sop", "hexCodepoint": "0xe9a1", "codepoint": 59809}, {"name": "sort-all", "css": "tsfont-sort-all", "hexCodepoint": "0xe9a2", "codepoint": 59810}, {"name": "sort-left", "css": "tsfont-sort-left", "hexCodepoint": "0xe9a3", "codepoint": 59811}, {"name": "sort-right", "css": "tsfont-sort-right", "hexCodepoint": "0xe9a4", "codepoint": 59812}, {"name": "spacing", "css": "tsfont-spacing", "hexCodepoint": "0xe9a5", "codepoint": 59813}, {"name": "spark", "css": "tsfont-spark", "hexCodepoint": "0xe9a6", "codepoint": 59814}, {"name": "square", "css": "tsfont-square", "hexCodepoint": "0xe9a7", "codepoint": 59815}, {"name": "stage", "css": "tsfont-stage", "hexCodepoint": "0xe9a8", "codepoint": 59816}, {"name": "star-border", "css": "tsfont-star-border", "hexCodepoint": "0xe9a9", "codepoint": 59817}, {"name": "star-half", "css": "tsfont-star-half", "hexCodepoint": "0xe9aa", "codepoint": 59818}, {"name": "star", "css": "tsfont-star", "hexCodepoint": "0xe9ab", "codepoint": 59819}, {"name": "stars", "css": "tsfont-stars", "hexCodepoint": "0xe9ac", "codepoint": 59820}, {"name": "storage", "css": "tsfont-storage", "hexCodepoint": "0xe9ad", "codepoint": 59821}, {"name": "storages", "css": "tsfont-storages", "hexCodepoint": "0xe9ae", "codepoint": 59822}, {"name": "storm", "css": "tsfont-storm", "hexCodepoint": "0xe9af", "codepoint": 59823}, {"name": "switch", "css": "tsfont-switch", "hexCodepoint": "0xe9b0", "codepoint": 59824}, {"name": "sybase", "css": "tsfont-sybase", "hexCodepoint": "0xe9b1", "codepoint": 59825}, {"name": "tabforword", "css": "tsfont-tabforword", "hexCodepoint": "0xe9b2", "codepoint": 59826}, {"name": "takeover", "css": "tsfont-takeover", "hexCodepoint": "0xe9b3", "codepoint": 59827}, {"name": "task-cancel", "css": "tsfont-task-cancel", "hexCodepoint": "0xe9b4", "codepoint": 59828}, {"name": "task-ok", "css": "tsfont-task-ok", "hexCodepoint": "0xe9b5", "codepoint": 59829}, {"name": "task", "css": "tsfont-task", "hexCodepoint": "0xe9b6", "codepoint": 59830}, {"name": "taskperson-s", "css": "tsfont-taskperson-s", "hexCodepoint": "0xe9b7", "codepoint": 59831}, {"name": "taskperson", "css": "tsfont-taskperson", "hexCodepoint": "0xe9b8", "codepoint": 59832}, {"name": "team-s", "css": "tsfont-team-s", "hexCodepoint": "0xe9b9", "codepoint": 59833}, {"name": "team", "css": "tsfont-team", "hexCodepoint": "0xe9ba", "codepoint": 59834}, {"name": "tencentcloud", "css": "tsfont-tencentcloud", "hexCodepoint": "0xe9bc", "codepoint": 59836}, {"name": "test", "css": "tsfont-test", "hexCodepoint": "0xe9bd", "codepoint": 59837}, {"name": "text-delete", "css": "tsfont-text-delete", "hexCodepoint": "0xe9be", "codepoint": 59838}, {"name": "textarea", "css": "tsfont-textarea", "hexCodepoint": "0xe9bf", "codepoint": 59839}, {"name": "theme", "css": "tsfont-theme", "hexCodepoint": "0xe9c0", "codepoint": 59840}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "css": "tsfont-t<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hexCodepoint": "0xe9c1", "codepoint": 59841}, {"name": "tickets", "css": "tsfont-tickets", "hexCodepoint": "0xe9c2", "codepoint": 59842}, {"name": "time", "css": "tsfont-time", "hexCodepoint": "0xe9c3", "codepoint": 59843}, {"name": "timer", "css": "tsfont-timer", "hexCodepoint": "0xe9c4", "codepoint": 59844}, {"name": "title", "css": "tsfont-title", "hexCodepoint": "0xe9c5", "codepoint": 59845}, {"name": "tomcat", "css": "tsfont-tomcat", "hexCodepoint": "0xe9c6", "codepoint": 59846}, {"name": "tool", "css": "tsfont-tool", "hexCodepoint": "0xe9c7", "codepoint": 59847}, {"name": "topo", "css": "tsfont-topo", "hexCodepoint": "0xe9c8", "codepoint": 59848}, {"name": "transmit", "css": "tsfont-transmit", "hexCodepoint": "0xe9c9", "codepoint": 59849}, {"name": "trash-o", "css": "tsfont-trash-o", "hexCodepoint": "0xe9ca", "codepoint": 59850}, {"name": "trash-s", "css": "tsfont-trash-s", "hexCodepoint": "0xe9cb", "codepoint": 59851}, {"name": "tree", "css": "tsfont-tree", "hexCodepoint": "0xe9cc", "codepoint": 59852}, {"name": "tuxedo", "css": "tsfont-tuxedo", "hexCodepoint": "0xe9cd", "codepoint": 59853}, {"name": "type", "css": "tsfont-type", "hexCodepoint": "0xe9ce", "codepoint": 59854}, {"name": "unbind", "css": "tsfont-unbind", "hexCodepoint": "0xe9cf", "codepoint": 59855}, {"name": "undo", "css": "tsfont-undo", "hexCodepoint": "0xe9d0", "codepoint": 59856}, {"name": "unfold", "css": "tsfont-unfold", "hexCodepoint": "0xe9d1", "codepoint": 59857}, {"name": "unknown", "css": "tsfont-unknown", "hexCodepoint": "0xe9d2", "codepoint": 59858}, {"name": "unlock", "css": "tsfont-unlock", "hexCodepoint": "0xe9d3", "codepoint": 59859}, {"name": "unsend", "css": "tsfont-unsend", "hexCodepoint": "0xe9d4", "codepoint": 59860}, {"name": "up", "css": "tsfont-up", "hexCodepoint": "0xe9d5", "codepoint": 59861}, {"name": "upload", "css": "tsfont-upload", "hexCodepoint": "0xe9d6", "codepoint": 59862}, {"name": "urgency", "css": "tsfont-urgency", "hexCodepoint": "0xe9d7", "codepoint": 59863}, {"name": "user-s", "css": "tsfont-user-s", "hexCodepoint": "0xe9d8", "codepoint": 59864}, {"name": "user-setting", "css": "tsfont-user-setting", "hexCodepoint": "0xe9d9", "codepoint": 59865}, {"name": "user", "css": "tsfont-user", "hexCodepoint": "0xe9da", "codepoint": 59866}, {"name": "userinfo", "css": "tsfont-userinfo", "hexCodepoint": "0xe9db", "codepoint": 59867}, {"name": "vcenter", "css": "tsfont-vcenter", "hexCodepoint": "0xe9dc", "codepoint": 59868}, {"name": "verify", "css": "tsfont-verify", "hexCodepoint": "0xe9dd", "codepoint": 59869}, {"name": "version", "css": "tsfont-version", "hexCodepoint": "0xe9de", "codepoint": 59870}, {"name": "vertical-bottom", "css": "tsfont-vertical-bottom", "hexCodepoint": "0xe9df", "codepoint": 59871}, {"name": "vertical-middle", "css": "tsfont-vertical-middle", "hexCodepoint": "0xe9e0", "codepoint": 59872}, {"name": "vertical-top", "css": "tsfont-vertical-top", "hexCodepoint": "0xe9e1", "codepoint": 59873}, {"name": "virtualmachine", "css": "tsfont-virtualmachine", "hexCodepoint": "0xe9e2", "codepoint": 59874}, {"name": "virtualstorage", "css": "tsfont-virtualstorage", "hexCodepoint": "0xe9e3", "codepoint": 59875}, {"name": "vm", "css": "tsfont-vm", "hexCodepoint": "0xe9e4", "codepoint": 59876}, {"name": "vmware-cluster", "css": "tsfont-vmware-cluster", "hexCodepoint": "0xe9e5", "codepoint": 59877}, {"name": "vote-o", "css": "tsfont-vote-o", "hexCodepoint": "0xe9e6", "codepoint": 59878}, {"name": "wangluoshujufangxielouxitong", "css": "tsfont-wangluoshujufangxielouxitong", "hexCodepoint": "0xe9e7", "codepoint": 59879}, {"name": "warning-o", "css": "tsfont-warning-o", "hexCodepoint": "0xe9e8", "codepoint": 59880}, {"name": "warning-s", "css": "tsfont-warning-s", "hexCodepoint": "0xe9e9", "codepoint": 59881}, {"name": "weblogic", "css": "tsfont-weblogic", "hexCodepoint": "0xe9ea", "codepoint": 59882}, {"name": "websphere", "css": "tsfont-websphere", "hexCodepoint": "0xe9eb", "codepoint": 59883}, {"name": "Webyingyongfanghuxitong", "css": "tsfont-Webyingyongfanghuxitong", "hexCodepoint": "0xe9ec", "codepoint": 59884}, {"name": "wechat", "css": "tsfont-wechat", "hexCodepoint": "0xe9ed", "codepoint": 59885}, {"name": "<PERSON><PERSON><PERSON>", "css": "tsfont-we<PERSON><PERSON>", "hexCodepoint": "0xe9ee", "codepoint": 59886}, {"name": "windows", "css": "tsfont-windows", "hexCodepoint": "0xe9ef", "codepoint": 59887}, {"name": "xitongpeizhi", "css": "tsfont-xitongpeizhi", "hexCodepoint": "0xe9f0", "codepoint": 59888}, {"name": "xunijizhuanyongshebeivpn", "css": "tsfont-xunijizhuanyongshebeivpn", "hexCodepoint": "0xe9f1", "codepoint": 59889}, {"name": "yitihuajigui", "css": "tsfont-yitihuajigui", "hexCodepoint": "0xe9f2", "codepoint": 59890}, {"name": "youjiananquanguolvxitong", "css": "tsfont-youjiananquanguolvxitong", "hexCodepoint": "0xe9f3", "codepoint": 59891}, {"name": "yun<PERSON><PERSON><PERSON>shebei", "css": "tsfont-y<PERSON><PERSON><PERSON><PERSON>shebei", "hexCodepoint": "0xe9f4", "codepoint": 59892}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "css": "tsfont-zhis<PERSON>ku", "hexCodepoint": "0xe9f5", "codepoint": 59893}, {"name": "zhunrukongzhishebei", "css": "tsfont-zhunrukongzhishebei", "hexCodepoint": "0xe9f6", "codepoint": 59894}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "css": "tsfont-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hexCodepoint": "0xe9f7", "codepoint": 59895}, {"name": "zidonghua", "css": "tsfont-zidonghua", "hexCodepoint": "0xe9f8", "codepoint": 59896}, {"name": "z<PERSON>nwu", "css": "tsfont-zirenwu", "hexCodepoint": "0xe9f9", "codepoint": 59897}, {"name": "zookeeper", "css": "ts<PERSON><PERSON>-zookeeper", "hexCodepoint": "0xe9fa", "codepoint": 59898}, {"name": "storagerpa", "css": "tsfont-storagerpa", "hexCodepoint": "0xe9fb", "codepoint": 59899}, {"name": "application_module", "css": "tsfont-application_module", "hexCodepoint": "0xe9fc", "codepoint": 59900}, {"name": "dbins", "css": "tsfont-dbins", "hexCodepoint": "0xe9fd", "codepoint": 59901}, {"name": "mssqlserver-db", "css": "tsfont-mssqlserver-db", "hexCodepoint": "0xe9fe", "codepoint": 59902}, {"name": "sybase-db", "css": "tsfont-sybase-db", "hexCodepoint": "0xe9ff", "codepoint": 59903}, {"name": "db2-db", "css": "tsfont-db2-db", "hexCodepoint": "0xea00", "codepoint": 59904}, {"name": "oracle-db", "css": "tsfont-oracle-db", "hexCodepoint": "0xea01", "codepoint": 59905}, {"name": "postgresql-db", "css": "tsfont-postgresql-db", "hexCodepoint": "0xea02", "codepoint": 59906}, {"name": "informix-db", "css": "tsfont-informix-db", "hexCodepoint": "0xea03", "codepoint": 59907}, {"name": "mysql-db", "css": "tsfont-mysql-db", "hexCodepoint": "0xea04", "codepoint": 59908}, {"name": "k8s_service", "css": "tsfont-k8s_service", "hexCodepoint": "0xea05", "codepoint": 59909}, {"name": "k8s_ingress", "css": "tsfont-k8s_ingress", "hexCodepoint": "0xea06", "codepoint": 59910}, {"name": "k8s_deployment", "css": "tsfont-k8s_deployment", "hexCodepoint": "0xea07", "codepoint": 59911}, {"name": "k8s_namespace", "css": "tsfont-k8s_namespace", "hexCodepoint": "0xea08", "codepoint": 59912}, {"name": "k8s_replicaset", "css": "tsfont-k8s_replicaset", "hexCodepoint": "0xea09", "codepoint": 59913}, {"name": "k8s_pod", "css": "tsfont-k8s_pod", "hexCodepoint": "0xea0a", "codepoint": 59914}, {"name": "k8s_node", "css": "tsfont-k8s_node", "hexCodepoint": "0xea0b", "codepoint": 59915}, {"name": "zookeepercluster", "css": "tsfont-zookeepercluster", "hexCodepoint": "0xea0c", "codepoint": 59916}, {"name": "vmware-datacenter", "css": "tsfont-vmware-datacenter", "hexCodepoint": "0xea0d", "codepoint": 59917}, {"name": "font-bg", "css": "tsfont-font-bg", "hexCodepoint": "0xea0e", "codepoint": 59918}, {"name": "font-color", "css": "tsfont-font-color", "hexCodepoint": "0xea0f", "codepoint": 59919}, {"name": "pin-angle-o", "css": "tsfont-pin-angle-o", "hexCodepoint": "0xea10", "codepoint": 59920}, {"name": "pin-angle-s", "css": "tsfont-pin-angle-s", "hexCodepoint": "0xea11", "codepoint": 59921}, {"name": "pin-o", "css": "tsfont-pin-o", "hexCodepoint": "0xea12", "codepoint": 59922}, {"name": "pin-s", "css": "tsfont-pin-s", "hexCodepoint": "0xea13", "codepoint": 59923}, {"name": "private-data-source", "css": "tsfont-private-data-source", "hexCodepoint": "0xea14", "codepoint": 59924}, {"name": "trigger", "css": "tsfont-trigger", "hexCodepoint": "0xea15", "codepoint": 59925}, {"name": "webhook", "css": "tsfont-webhook", "hexCodepoint": "0xea16", "codepoint": 59926}, {"name": "width", "css": "tsfont-width", "hexCodepoint": "0xea17", "codepoint": 59927}, {"name": "neatlogic", "css": "tsfont-neatlogic", "hexCodepoint": "0xea18", "codepoint": 59928}, {"name": "gitlab", "css": "tsfont-gitlab", "hexCodepoint": "0xea19", "codepoint": 59929}, {"name": "svn", "css": "tsfont-svn", "hexCodepoint": "0xea1a", "codepoint": 59930}, {"name": "merge", "css": "tsfont-merge", "hexCodepoint": "0xea1b", "codepoint": 59931}, {"name": "table-column", "css": "tsfont-table-column", "hexCodepoint": "0xea1c", "codepoint": 59932}, {"name": "table-row", "css": "tsfont-table-row", "hexCodepoint": "0xea1d", "codepoint": 59933}, {"name": "check-square-o", "css": "tsfont-check-square-o", "hexCodepoint": "0xea1e", "codepoint": 59934}, {"name": "double-arrow-left", "css": "tsfont-double-arrow-left", "hexCodepoint": "0xea1f", "codepoint": 59935}, {"name": "double-arrow-right", "css": "tsfont-double-arrow-right", "hexCodepoint": "0xea20", "codepoint": 59936}, {"name": "double-arrow-up", "css": "tsfont-double-arrow-up", "hexCodepoint": "0xea21", "codepoint": 59937}, {"name": "heart-o", "css": "tsfont-heart-o", "hexCodepoint": "0xea22", "codepoint": 59938}, {"name": "folder-open", "css": "tsfont-folder-open", "hexCodepoint": "0xea23", "codepoint": 59939}, {"name": "solid-circle", "css": "tsfont-solid-circle", "hexCodepoint": "0xea24", "codepoint": 59940}, {"name": "alert", "css": "tsfont-alert", "hexCodepoint": "0xea25", "codepoint": 59941}, {"name": "batch-ops", "css": "tsfont-batch-ops", "hexCodepoint": "0xea26", "codepoint": 59942}, {"name": "listsetting", "css": "tsfont-listsetting", "hexCodepoint": "0xea27", "codepoint": 59943}, {"name": "m-apm", "css": "tsfont-m-apm", "hexCodepoint": "0xea28", "codepoint": 59944}, {"name": "m-batchdeploy", "css": "tsfont-m-batchdeploy", "hexCodepoint": "0xea29", "codepoint": 59945}, {"name": "m-dashboard-job", "css": "tsfont-m-dashboard-job", "hexCodepoint": "0xea2a", "codepoint": 59946}, {"name": "m-dashboard", "css": "tsfont-m-dashboard", "hexCodepoint": "0xea2b", "codepoint": 59947}, {"name": "m-deployment", "css": "tsfont-m-deployment", "hexCodepoint": "0xea2c", "codepoint": 59948}, {"name": "m-ip", "css": "tsfont-m-ip", "hexCodepoint": "0xea2d", "codepoint": 59949}, {"name": "m-octopus", "css": "tsfont-m-octopus", "hexCodepoint": "0xea2e", "codepoint": 59950}, {"name": "m-request", "css": "tsfont-m-request", "hexCodepoint": "0xea2f", "codepoint": 59951}, {"name": "m-signature", "css": "tsfont-m-signature", "hexCodepoint": "0xea30", "codepoint": 59952}, {"name": "m-stack", "css": "tsfont-m-stack", "hexCodepoint": "0xea31", "codepoint": 59953}, {"name": "spinner", "css": "tsfont-spinner", "hexCodepoint": "0xea32", "codepoint": 59954}, {"name": "tags", "css": "tsfont-tags", "hexCodepoint": "0xea33", "codepoint": 59955}, {"name": "border-bottom", "css": "tsfont-border-bottom", "hexCodepoint": "0xea34", "codepoint": 59956}, {"name": "border-horizontal", "css": "tsfont-border-horizontal", "hexCodepoint": "0xea35", "codepoint": 59957}, {"name": "border-left", "css": "tsfont-border-left", "hexCodepoint": "0xea36", "codepoint": 59958}, {"name": "border-right", "css": "tsfont-border-right", "hexCodepoint": "0xea37", "codepoint": 59959}, {"name": "border-top", "css": "tsfont-border-top", "hexCodepoint": "0xea38", "codepoint": 59960}, {"name": "border-vertical", "css": "tsfont-border-vertical", "hexCodepoint": "0xea39", "codepoint": 59961}, {"name": "bottom", "css": "tsfont-bottom", "hexCodepoint": "0xea3a", "codepoint": 59962}, {"name": "double-arrow-down", "css": "tsfont-double-arrow-down", "hexCodepoint": "0xea3b", "codepoint": 59963}, {"name": "top", "css": "tsfont-top", "hexCodepoint": "0xea3c", "codepoint": 59964}, {"name": "ts", "css": "tsfont-ts", "hexCodepoint": "0xea3d", "codepoint": 59965}, {"name": "paste", "css": "tsfont-paste", "hexCodepoint": "0xea3e", "codepoint": 59966}, {"name": "dataconversion", "css": "tsfont-dataconversion", "hexCodepoint": "0xea3f", "codepoint": 59967}, {"name": "rotate-left", "css": "tsfont-rotate-left", "hexCodepoint": "0xea40", "codepoint": 59968}, {"name": "scale-to-original", "css": "tsfont-scale-to-original", "hexCodepoint": "0xea41", "codepoint": 59969}, {"name": "zoom-in", "css": "tsfont-zoom-in", "hexCodepoint": "0xea42", "codepoint": 59970}, {"name": "zoom-out", "css": "tsfont-zoom-out", "hexCodepoint": "0xea43", "codepoint": 59971}, {"name": "snapshot", "css": "tsfont-snapshot", "hexCodepoint": "0xea44", "codepoint": 59972}]