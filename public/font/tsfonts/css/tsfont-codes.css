/* DO NOT EDIT! Generated by iconfount */


.tsfont-a10:before { content: '\e800'; } /* '' */
.tsfont-accessendpoint:before { content: '\e801'; } /* '' */
.tsfont-action:before { content: '\e802'; } /* '' */
.tsfont-activemq:before { content: '\e803'; } /* '' */
.tsfont-adapter:before { content: '\e804'; } /* '' */
.tsfont-addchange:before { content: '\e805'; } /* '' */
.tsfont-addimg-s:before { content: '\e806'; } /* '' */
.tsfont-addimg:before { content: '\e807'; } /* '' */
.tsfont-addtag:before { content: '\e808'; } /* '' */
.tsfont-addteam:before { content: '\e809'; } /* '' */
.tsfont-adduser:before { content: '\e80a'; } /* '' */
.tsfont-adjust:before { content: '\e80b'; } /* '' */
.tsfont-agent:before { content: '\e80c'; } /* '' */
.tsfont-aix:before { content: '\e80d'; } /* '' */
.tsfont-alicloud:before { content: '\e80e'; } /* '' */
.tsfont-anquangeliwangzha:before { content: '\e80f'; } /* '' */
.tsfont-anquanguanlipingtai:before { content: '\e810'; } /* '' */
.tsfont-anquanwangguan:before { content: '\e811'; } /* '' */
.tsfont-anzhuangbao:before { content: '\e812'; } /* '' */
.tsfont-anzhuangshuxing:before { content: '\e813'; } /* '' */
.tsfont-apache:before { content: '\e814'; } /* '' */
.tsfont-app:before { content: '\e815'; } /* '' */
.tsfont-apple:before { content: '\e816'; } /* '' */
.tsfont-application:before { content: '\e817'; } /* '' */
.tsfont-apps:before { content: '\e818'; } /* '' */
.tsfont-arrow-corner-left:before { content: '\e819'; } /* '' */
.tsfont-arrow-corner-right:before { content: '\e81a'; } /* '' */
.tsfont-arrow-down:before { content: '\e81b'; } /* '' */
.tsfont-arrow-left:before { content: '\e81c'; } /* '' */
.tsfont-arrow-right:before { content: '\e81d'; } /* '' */
.tsfont-arrow-up:before { content: '\e81e'; } /* '' */
.tsfont-attachment:before { content: '\e81f'; } /* '' */
.tsfont-attribute:before { content: '\e820'; } /* '' */
.tsfont-auth:before { content: '\e821'; } /* '' */
.tsfont-auto:before { content: '\e822'; } /* '' */
.tsfont-aws:before { content: '\e823'; } /* '' */
.tsfont-bad:before { content: '\e824'; } /* '' */
.tsfont-badge:before { content: '\e825'; } /* '' */
.tsfont-baobiao:before { content: '\e826'; } /* '' */
.tsfont-baosong:before { content: '\e827'; } /* '' */
.tsfont-bar:before { content: '\e828'; } /* '' */
.tsfont-barlist:before { content: '\e829'; } /* '' */
.tsfont-batch-download:before { content: '\e82a'; } /* '' */
.tsfont-batch-success:before { content: '\e82b'; } /* '' */
.tsfont-batch-upload:before { content: '\e82c'; } /* '' */
.tsfont-bell-o:before { content: '\e82d'; } /* '' */
.tsfont-bell-off:before { content: '\e82e'; } /* '' */
.tsfont-bell-on:before { content: '\e82f'; } /* '' */
.tsfont-bell-z:before { content: '\e830'; } /* '' */
.tsfont-bell:before { content: '\e831'; } /* '' */
.tsfont-biaoti:before { content: '\e832'; } /* '' */
.tsfont-bind:before { content: '\e833'; } /* '' */
.tsfont-block:before { content: '\e834'; } /* '' */
.tsfont-blocklist:before { content: '\e835'; } /* '' */
.tsfont-blocks:before { content: '\e836'; } /* '' */
.tsfont-bofenfuyongshebei:before { content: '\e837'; } /* '' */
.tsfont-bold:before { content: '\e838'; } /* '' */
.tsfont-book:before { content: '\e839'; } /* '' */
.tsfont-border-all:before { content: '\e83a'; } /* '' */
.tsfont-border-clear:before { content: '\e83b'; } /* '' */
.tsfont-broom:before { content: '\e83c'; } /* '' */
.tsfont-browser:before { content: '\e83d'; } /* '' */
.tsfont-bushushuxing:before { content: '\e83e'; } /* '' */
.tsfont-calendar:before { content: '\e83f'; } /* '' */
.tsfont-callcenter:before { content: '\e840'; } /* '' */
.tsfont-canvas:before { content: '\e841'; } /* '' */
.tsfont-celve:before { content: '\e842'; } /* '' */
.tsfont-center:before { content: '\e843'; } /* '' */
.tsfont-centos:before { content: '\e844'; } /* '' */
.tsfont-certificate:before { content: '\e845'; } /* '' */
.tsfont-change:before { content: '\e846'; } /* '' */
.tsfont-changing:before { content: '\e847'; } /* '' */
.tsfont-chart-area:before { content: '\e848'; } /* '' */
.tsfont-chart-bar:before { content: '\e849'; } /* '' */
.tsfont-chart-bullet:before { content: '\e84a'; } /* '' */
.tsfont-chart-funnel:before { content: '\e84b'; } /* '' */
.tsfont-chart-gauge:before { content: '\e84c'; } /* '' */
.tsfont-chart-groupedcolumn:before { content: '\e84d'; } /* '' */
.tsfont-chart-heatmap:before { content: '\e84e'; } /* '' */
.tsfont-chart-line:before { content: '\e84f'; } /* '' */
.tsfont-chart-lines:before { content: '\e850'; } /* '' */
.tsfont-chart-liquid:before { content: '\e851'; } /* '' */
.tsfont-chart-number:before { content: '\e852'; } /* '' */
.tsfont-chart-pie:before { content: '\e853'; } /* '' */
.tsfont-chart-polyline:before { content: '\e854'; } /* '' */
.tsfont-chart-progress:before { content: '\e855'; } /* '' */
.tsfont-chart-radar:before { content: '\e856'; } /* '' */
.tsfont-chart-rose:before { content: '\e857'; } /* '' */
.tsfont-chart-sankey:before { content: '\e858'; } /* '' */
.tsfont-chart-scatter:before { content: '\e859'; } /* '' */
.tsfont-chart-scatterbubble:before { content: '\e85a'; } /* '' */
.tsfont-chart-stackedcolumn:before { content: '\e85b'; } /* '' */
.tsfont-chart-table:before { content: '\e85c'; } /* '' */
.tsfont-chart-wordcloud:before { content: '\e85e'; } /* '' */
.tsfont-check-o:before { content: '\e85f'; } /* '' */
.tsfont-check-s:before { content: '\e860'; } /* '' */
.tsfont-check-some:before { content: '\e861'; } /* '' */
.tsfont-check-square:before { content: '\e862'; } /* '' */
.tsfont-check:before { content: '\e863'; } /* '' */
.tsfont-checklist:before { content: '\e864'; } /* '' */
.tsfont-ci-o:before { content: '\e865'; } /* '' */
.tsfont-ci:before { content: '\e866'; } /* '' */
.tsfont-cidaiku:before { content: '\e867'; } /* '' */
.tsfont-cidaizhenlie:before { content: '\e868'; } /* '' */
.tsfont-cientityselect:before { content: '\e869'; } /* '' */
.tsfont-circle-o:before { content: '\e86a'; } /* '' */
.tsfont-circle:before { content: '\e86b'; } /* '' */
.tsfont-circulation-o:before { content: '\e86c'; } /* '' */
.tsfont-circulation-s:before { content: '\e86d'; } /* '' */
.tsfont-cisco:before { content: '\e86e'; } /* '' */
.tsfont-close-o:before { content: '\e86f'; } /* '' */
.tsfont-close-s:before { content: '\e870'; } /* '' */
.tsfont-close:before { content: '\e871'; } /* '' */
.tsfont-cloud:before { content: '\e872'; } /* '' */
.tsfont-cluster-mode:before { content: '\e873'; } /* '' */
.tsfont-cluster-software:before { content: '\e874'; } /* '' */
.tsfont-cluster:before { content: '\e875'; } /* '' */
.tsfont-code:before { content: '\e876'; } /* '' */
.tsfont-collapse:before { content: '\e877'; } /* '' */
.tsfont-common:before { content: '\e878'; } /* '' */
.tsfont-compare:before { content: '\e879'; } /* '' */
.tsfont-component:before { content: '\e87a'; } /* '' */
.tsfont-config:before { content: '\e87b'; } /* '' */
.tsfont-connector:before { content: '\e87c'; } /* '' */
.tsfont-console:before { content: '\e87d'; } /* '' */
.tsfont-cooperate-o:before { content: '\e87e'; } /* '' */
.tsfont-cooperate-s:before { content: '\e87f'; } /* '' */
.tsfont-copy:before { content: '\e880'; } /* '' */
.tsfont-danger-level:before { content: '\e881'; } /* '' */
.tsfont-danger-o:before { content: '\e882'; } /* '' */
.tsfont-danger-s:before { content: '\e883'; } /* '' */
.tsfont-datacenter:before { content: '\e884'; } /* '' */
.tsfont-day:before { content: '\e885'; } /* '' */
.tsfont-db-cluster:before { content: '\e886'; } /* '' */
.tsfont-db-ins:before { content: '\e887'; } /* '' */
.tsfont-db:before { content: '\e888'; } /* '' */
.tsfont-db2:before { content: '\e889'; } /* '' */
.tsfont-debug:before { content: '\e88a'; } /* '' */
.tsfont-dell:before { content: '\e88b'; } /* '' */
.tsfont-desktop:before { content: '\e88c'; } /* '' */
.tsfont-devices:before { content: '\e88d'; } /* '' */
.tsfont-dictionary:before { content: '\e88e'; } /* '' */
.tsfont-dns:before { content: '\e88f'; } /* '' */
.tsfont-docker:before { content: '\e890'; } /* '' */
.tsfont-dot:before { content: '\e891'; } /* '' */
.tsfont-down:before { content: '\e892'; } /* '' */
.tsfont-download:before { content: '\e893'; } /* '' */
.tsfont-drafts:before { content: '\e894'; } /* '' */
.tsfont-drag:before { content: '\e895'; } /* '' */
.tsfont-drop-down-s:before { content: '\e896'; } /* '' */
.tsfont-drop-down:before { content: '\e897'; } /* '' */
.tsfont-drop-left:before { content: '\e898'; } /* '' */
.tsfont-drop-right:before { content: '\e899'; } /* '' */
.tsfont-drop-up:before { content: '\e89a'; } /* '' */
.tsfont-duixiangcunchu:before { content: '\e89b'; } /* '' */
.tsfont-edit-s:before { content: '\e89c'; } /* '' */
.tsfont-edit:before { content: '\e89d'; } /* '' */
.tsfont-elasticsearch:before { content: '\e89e'; } /* '' */
.tsfont-empty:before { content: '\e89f'; } /* '' */
.tsfont-eoa:before { content: '\e8a0'; } /* '' */
.tsfont-excellent:before { content: '\e8a1'; } /* '' */
.tsfont-expand:before { content: '\e8a2'; } /* '' */
.tsfont-export-s:before { content: '\e8a3'; } /* '' */
.tsfont-export:before { content: '\e8a4'; } /* '' */
.tsfont-eye-off:before { content: '\e8a5'; } /* '' */
.tsfont-eye:before { content: '\e8a6'; } /* '' */
.tsfont-f5:before { content: '\e8a7'; } /* '' */
.tsfont-fangbingduwangguan:before { content: '\e8a8'; } /* '' */
.tsfont-fcdev:before { content: '\e8a9'; } /* '' */
.tsfont-fcswitch:before { content: '\e8aa'; } /* '' */
.tsfont-file-multi:before { content: '\e8ab'; } /* '' */
.tsfont-file-single:before { content: '\e8ac'; } /* '' */
.tsfont-filter-o:before { content: '\e8ad'; } /* '' */
.tsfont-filter:before { content: '\e8ae'; } /* '' */
.tsfont-firewall:before { content: '\e8af'; } /* '' */
.tsfont-first:before { content: '\e8b0'; } /* '' */
.tsfont-flow-children:before { content: '\e8b1'; } /* '' */
.tsfont-flow-siblings:before { content: '\e8b2'; } /* '' */
.tsfont-flow:before { content: '\e8b3'; } /* '' */
.tsfont-fold:before { content: '\e8b4'; } /* '' */
.tsfont-folder-o:before { content: '\e8b5'; } /* '' */
.tsfont-folder-s:before { content: '\e8b6'; } /* '' */
.tsfont-folder-share:before { content: '\e8b7'; } /* '' */
.tsfont-font-size:before { content: '\e8b8'; } /* '' */
.tsfont-forbid:before { content: '\e8b9'; } /* '' */
.tsfont-formcascadelist:before { content: '\e8ba'; } /* '' */
.tsfont-formdynamiclist:before { content: '\e8bb'; } /* '' */
.tsfont-forminput:before { content: '\e8bc'; } /* '' */
.tsfont-formlink:before { content: '\e8bd'; } /* '' */
.tsfont-formlist:before { content: '\e8be'; } /* '' */
.tsfont-formselect:before { content: '\e8bf'; } /* '' */
.tsfont-formselectcascader:before { content: '\e8c0'; } /* '' */
.tsfont-formstaticlist:before { content: '\e8c1'; } /* '' */
.tsfont-formtextarea:before { content: '\e8c2'; } /* '' */
.tsfont-formtime:before { content: '\e8c3'; } /* '' */
.tsfont-freebsd:before { content: '\e8c4'; } /* '' */
.tsfont-fullscreen:before { content: '\e8c5'; } /* '' */
.tsfont-fuwuqi:before { content: '\e8c6'; } /* '' */
.tsfont-good:before { content: '\e8c7'; } /* '' */
.tsfont-group:before { content: '\e8c8'; } /* '' */
.tsfont-hadoop:before { content: '\e8c9'; } /* '' */
.tsfont-hand:before { content: '\e8ca'; } /* '' */
.tsfont-hardware:before { content: '\e8cb'; } /* '' */
.tsfont-heart-s:before { content: '\e8cc'; } /* '' */
.tsfont-history:before { content: '\e8cd'; } /* '' */
.tsfont-home:before { content: '\e8ce'; } /* '' */
.tsfont-horizontal-center:before { content: '\e8cf'; } /* '' */
.tsfont-horizontal-justify:before { content: '\e8d0'; } /* '' */
.tsfont-horizontal-left:before { content: '\e8d1'; } /* '' */
.tsfont-horizontal-right:before { content: '\e8d2'; } /* '' */
.tsfont-host:before { content: '\e8d3'; } /* '' */
.tsfont-hp:before { content: '\e8d4'; } /* '' */
.tsfont-huawei:before { content: '\e8d5'; } /* '' */
.tsfont-ibm:before { content: '\e8d6'; } /* '' */
.tsfont-iis:before { content: '\e8d7'; } /* '' */
.tsfont-image-s:before { content: '\e8d8'; } /* '' */
.tsfont-image:before { content: '\e8d9'; } /* '' */
.tsfont-img-center:before { content: '\e8da'; } /* '' */
.tsfont-img-left:before { content: '\e8db'; } /* '' */
.tsfont-img-right:before { content: '\e8dc'; } /* '' */
.tsfont-import-s:before { content: '\e8dd'; } /* '' */
.tsfont-import:before { content: '\e8de'; } /* '' */
.tsfont-indent:before { content: '\e8df'; } /* '' */
.tsfont-info-o:before { content: '\e8e0'; } /* '' */
.tsfont-info-s:before { content: '\e8e1'; } /* '' */
.tsfont-info:before { content: '\e8e2'; } /* '' */
.tsfont-informix:before { content: '\e8e3'; } /* '' */
.tsfont-inlink:before { content: '\e8e4'; } /* '' */
.tsfont-ins:before { content: '\e8e5'; } /* '' */
.tsfont-inspection:before { content: '\e8e6'; } /* '' */
.tsfont-inspur:before { content: '\e8e7'; } /* '' */
.tsfont-instance:before { content: '\e8e8'; } /* '' */
.tsfont-integration:before { content: '\e8e9'; } /* '' */
.tsfont-internet:before { content: '\e8ea'; } /* '' */
.tsfont-ip-list:before { content: '\e8eb'; } /* '' */
.tsfont-ip-object:before { content: '\e8ec'; } /* '' */
.tsfont-ipliebiao:before { content: '\e8ed'; } /* '' */
.tsfont-italic:before { content: '\e8ee'; } /* '' */
.tsfont-ITfuwu:before { content: '\e8ef'; } /* '' */
.tsfont-java:before { content: '\e8f0'; } /* '' */
.tsfont-jboss:before { content: '\e8f1'; } /* '' */
.tsfont-jetty:before { content: '\e8f2'; } /* '' */
.tsfont-jiamishebei:before { content: '\e8f3'; } /* '' */
.tsfont-jicheng:before { content: '\e8f4'; } /* '' */
.tsfont-jifang:before { content: '\e8f5'; } /* '' */
.tsfont-json:before { content: '\e8f6'; } /* '' */
.tsfont-juniper:before { content: '\e8f7'; } /* '' */
.tsfont-k8s:before { content: '\e8f8'; } /* '' */
.tsfont-kafka:before { content: '\e8f9'; } /* '' */
.tsfont-kangjujuefuwushebei:before { content: '\e8fa'; } /* '' */
.tsfont-keepalive:before { content: '\e8fb'; } /* '' */
.tsfont-label-s:before { content: '\e8fc'; } /* '' */
.tsfont-label:before { content: '\e8fd'; } /* '' */
.tsfont-last:before { content: '\e8fe'; } /* '' */
.tsfont-layer:before { content: '\e8ff'; } /* '' */
.tsfont-left:before { content: '\e900'; } /* '' */
.tsfont-lightning:before { content: '\e901'; } /* '' */
.tsfont-lighttpd:before { content: '\e902'; } /* '' */
.tsfont-linux:before { content: '\e903'; } /* '' */
.tsfont-list:before { content: '\e904'; } /* '' */
.tsfont-liuliangfenxishebei:before { content: '\e905'; } /* '' */
.tsfont-loadblance-vs:before { content: '\e906'; } /* '' */
.tsfont-location-o:before { content: '\e907'; } /* '' */
.tsfont-location:before { content: '\e908'; } /* '' */
.tsfont-lock-s:before { content: '\e909'; } /* '' */
.tsfont-lock:before { content: '\e90a'; } /* '' */
.tsfont-loudongsaomiaoshebei:before { content: '\e90b'; } /* '' */
.tsfont-lun:before { content: '\e90c'; } /* '' */
.tsfont-mail-o:before { content: '\e90d'; } /* '' */
.tsfont-mail-read-o:before { content: '\e90e'; } /* '' */
.tsfont-mail-s:before { content: '\e90f'; } /* '' */
.tsfont-mail-unread-o:before { content: '\e910'; } /* '' */
.tsfont-manager:before { content: '\e911'; } /* '' */
.tsfont-mark-all:before { content: '\e912'; } /* '' */
.tsfont-memcached:before { content: '\e913'; } /* '' */
.tsfont-mesos:before { content: '\e914'; } /* '' */
.tsfont-message-o:before { content: '\e915'; } /* '' */
.tsfont-message:before { content: '\e916'; } /* '' */
.tsfont-minus-o:before { content: '\e917'; } /* '' */
.tsfont-minus-s:before { content: '\e918'; } /* '' */
.tsfont-minus-square:before { content: '\e919'; } /* '' */
.tsfont-minus:before { content: '\e91a'; } /* '' */
.tsfont-mm-bat:before { content: '\e91b'; } /* '' */
.tsfont-mm-bmp:before { content: '\e91c'; } /* '' */
.tsfont-mm-cls:before { content: '\e91d'; } /* '' */
.tsfont-mm-cmd:before { content: '\e91e'; } /* '' */
.tsfont-mm-cnf:before { content: '\e91f'; } /* '' */
.tsfont-mm-css:before { content: '\e920'; } /* '' */
.tsfont-mm-dir:before { content: '\e921'; } /* '' */
.tsfont-mm-doc:before { content: '\e922'; } /* '' */
.tsfont-mm-docx:before { content: '\e923'; } /* '' */
.tsfont-mm-exe:before { content: '\e924'; } /* '' */
.tsfont-mm-gif:before { content: '\e925'; } /* '' */
.tsfont-mm-gzip:before { content: '\e926'; } /* '' */
.tsfont-mm-html:before { content: '\e927'; } /* '' */
.tsfont-mm-java:before { content: '\e928'; } /* '' */
.tsfont-mm-jpeg:before { content: '\e929'; } /* '' */
.tsfont-mm-jpg:before { content: '\e92a'; } /* '' */
.tsfont-mm-js:before { content: '\e92b'; } /* '' */
.tsfont-mm-misc:before { content: '\e92c'; } /* '' */
.tsfont-mm-mov:before { content: '\e92d'; } /* '' */
.tsfont-mm-mp4:before { content: '\e92e'; } /* '' */
.tsfont-mm-png:before { content: '\e92f'; } /* '' */
.tsfont-mm-ppt:before { content: '\e930'; } /* '' */
.tsfont-mm-py:before { content: '\e931'; } /* '' */
.tsfont-mm-rar:before { content: '\e932'; } /* '' */
.tsfont-mm-rpm:before { content: '\e933'; } /* '' */
.tsfont-mm-rtf:before { content: '\e934'; } /* '' */
.tsfont-mm-sh:before { content: '\e935'; } /* '' */
.tsfont-mm-sql:before { content: '\e936'; } /* '' */
.tsfont-mm-svg:before { content: '\e937'; } /* '' */
.tsfont-mm-tar:before { content: '\e938'; } /* '' */
.tsfont-mm-txt:before { content: '\e939'; } /* '' */
.tsfont-mm-unknown:before { content: '\e93a'; } /* '' */
.tsfont-mm-vbs:before { content: '\e93b'; } /* '' */
.tsfont-mm-xls:before { content: '\e93c'; } /* '' */
.tsfont-mm-xlsm:before { content: '\e93d'; } /* '' */
.tsfont-mm-xlsx:before { content: '\e93e'; } /* '' */
.tsfont-mm-xml:before { content: '\e93f'; } /* '' */
.tsfont-mm-zip:before { content: '\e940'; } /* '' */
.tsfont-module:before { content: '\e941'; } /* '' */
.tsfont-modules:before { content: '\e942'; } /* '' */
.tsfont-mongodb:before { content: '\e943'; } /* '' */
.tsfont-monitor:before { content: '\e944'; } /* '' */
.tsfont-mssqlserver:before { content: '\e945'; } /* '' */
.tsfont-mysql:before { content: '\e946'; } /* '' */
.tsfont-net-area:before { content: '\e947'; } /* '' */
.tsfont-netarea:before { content: '\e948'; } /* '' */
.tsfont-netdev:before { content: '\e949'; } /* '' */
.tsfont-nginx:before { content: '\e94a'; } /* '' */
.tsfont-night:before { content: '\e94b'; } /* '' */
.tsfont-node:before { content: '\e94c'; } /* '' */
.tsfont-node1:before { content: '\e94d'; } /* '' */
.tsfont-non-auth:before { content: '\e94e'; } /* '' */
.tsfont-novmtool:before { content: '\e94f'; } /* '' */
.tsfont-nutanix:before { content: '\e950'; } /* '' */
.tsfont-off-fullscreen:before { content: '\e951'; } /* '' */
.tsfont-openstack:before { content: '\e952'; } /* '' */
.tsfont-option-horizontal:before { content: '\e953'; } /* '' */
.tsfont-option-vertical:before { content: '\e954'; } /* '' */
.tsfont-oracle-rac:before { content: '\e955'; } /* '' */
.tsfont-oracle:before { content: '\e956'; } /* '' */
.tsfont-orderlist:before { content: '\e957'; } /* '' */
.tsfont-ordinary:before { content: '\e958'; } /* '' */
.tsfont-os-cluster:before { content: '\e959'; } /* '' */
.tsfont-os:before { content: '\e95a'; } /* '' */
.tsfont-outdent:before { content: '\e95b'; } /* '' */
.tsfont-outline-s:before { content: '\e95c'; } /* '' */
.tsfont-palette:before { content: '\e95d'; } /* '' */
.tsfont-pause-o:before { content: '\e95e'; } /* '' */
.tsfont-pause-s:before { content: '\e95f'; } /* '' */
.tsfont-pause:before { content: '\e960'; } /* '' */
.tsfont-peizhiguanli:before { content: '\e961'; } /* '' */
.tsfont-peizhihechashebei:before { content: '\e962'; } /* '' */
.tsfont-permission:before { content: '\e963'; } /* '' */
.tsfont-phone:before { content: '\e964'; } /* '' */
.tsfont-php:before { content: '\e965'; } /* '' */
.tsfont-pie:before { content: '\e966'; } /* '' */
.tsfont-pingbijigui:before { content: '\e967'; } /* '' */
.tsfont-play-o:before { content: '\e968'; } /* '' */
.tsfont-play-s:before { content: '\e969'; } /* '' */
.tsfont-play:before { content: '\e96a'; } /* '' */
.tsfont-plugin:before { content: '\e96b'; } /* '' */
.tsfont-plus-o:before { content: '\e96c'; } /* '' */
.tsfont-plus-s:before { content: '\e96d'; } /* '' */
.tsfont-plus-square:before { content: '\e96e'; } /* '' */
.tsfont-plus:before { content: '\e96f'; } /* '' */
.tsfont-pod:before { content: '\e970'; } /* '' */
.tsfont-port:before { content: '\e971'; } /* '' */
.tsfont-postgresql:before { content: '\e972'; } /* '' */
.tsfont-proxy:before { content: '\e973'; } /* '' */
.tsfont-pulse:before { content: '\e974'; } /* '' */
.tsfont-putongjigui:before { content: '\e975'; } /* '' */
.tsfont-python:before { content: '\e976'; } /* '' */
.tsfont-question-o:before { content: '\e977'; } /* '' */
.tsfont-question-s:before { content: '\e978'; } /* '' */
.tsfont-question:before { content: '\e979'; } /* '' */
.tsfont-rabbitmq:before { content: '\e97a'; } /* '' */
.tsfont-redhat:before { content: '\e97b'; } /* '' */
.tsfont-redis:before { content: '\e97c'; } /* '' */
.tsfont-refresh:before { content: '\e97d'; } /* '' */
.tsfont-reminder:before { content: '\e97e'; } /* '' */
.tsfont-reply-all:before { content: '\e97f'; } /* '' */
.tsfont-reply:before { content: '\e980'; } /* '' */
.tsfont-report:before { content: '\e981'; } /* '' */
.tsfont-resin:before { content: '\e982'; } /* '' */
.tsfont-restart:before { content: '\e983'; } /* '' */
.tsfont-restful:before { content: '\e984'; } /* '' */
.tsfont-revover:before { content: '\e985'; } /* '' */
.tsfont-right:before { content: '\e986'; } /* '' */
.tsfont-riqishijian:before { content: '\e987'; } /* '' */
.tsfont-rizhishoujiyufenxixitong:before { content: '\e988'; } /* '' */
.tsfont-role-s:before { content: '\e989'; } /* '' */
.tsfont-role:before { content: '\e98a'; } /* '' */
.tsfont-rotate-right:before { content: '\e98b'; } /* '' */
.tsfont-router:before { content: '\e98c'; } /* '' */
.tsfont-run:before { content: '\e98d'; } /* '' */
.tsfont-ruqinjianceyufangyushebei:before { content: '\e98e'; } /* '' */
.tsfont-save:before { content: '\e98f'; } /* '' */
.tsfont-scene:before { content: '\e990'; } /* '' */
.tsfont-script:before { content: '\e991'; } /* '' */
.tsfont-search-minus:before { content: '\e992'; } /* '' */
.tsfont-search-plus:before { content: '\e993'; } /* '' */
.tsfont-search:before { content: '\e994'; } /* '' */
.tsfont-secdev:before { content: '\e995'; } /* '' */
.tsfont-security:before { content: '\e996'; } /* '' */
.tsfont-send:before { content: '\e997'; } /* '' */
.tsfont-setting:before { content: '\e998'; } /* '' */
.tsfont-shangwanghangweiguanlishebei:before { content: '\e999'; } /* '' */
.tsfont-share:before { content: '\e99a'; } /* '' */
.tsfont-shitu:before { content: '\e99b'; } /* '' */
.tsfont-shujukutuomin:before { content: '\e99c'; } /* '' */
.tsfont-shunt:before { content: '\e99d'; } /* '' */
.tsfont-shuziqianmingshebei:before { content: '\e99e'; } /* '' */
.tsfont-sla:before { content: '\e99f'; } /* '' */
.tsfont-softwareservice:before { content: '\e9a0'; } /* '' */
.tsfont-sop:before { content: '\e9a1'; } /* '' */
.tsfont-sort-all:before { content: '\e9a2'; } /* '' */
.tsfont-sort-left:before { content: '\e9a3'; } /* '' */
.tsfont-sort-right:before { content: '\e9a4'; } /* '' */
.tsfont-spacing:before { content: '\e9a5'; } /* '' */
.tsfont-spark:before { content: '\e9a6'; } /* '' */
.tsfont-square:before { content: '\e9a7'; } /* '' */
.tsfont-stage:before { content: '\e9a8'; } /* '' */
.tsfont-star-border:before { content: '\e9a9'; } /* '' */
.tsfont-star-half:before { content: '\e9aa'; } /* '' */
.tsfont-star:before { content: '\e9ab'; } /* '' */
.tsfont-stars:before { content: '\e9ac'; } /* '' */
.tsfont-storage:before { content: '\e9ad'; } /* '' */
.tsfont-storages:before { content: '\e9ae'; } /* '' */
.tsfont-storm:before { content: '\e9af'; } /* '' */
.tsfont-switch:before { content: '\e9b0'; } /* '' */
.tsfont-sybase:before { content: '\e9b1'; } /* '' */
.tsfont-tabforword:before { content: '\e9b2'; } /* '' */
.tsfont-takeover:before { content: '\e9b3'; } /* '' */
.tsfont-task-cancel:before { content: '\e9b4'; } /* '' */
.tsfont-task-ok:before { content: '\e9b5'; } /* '' */
.tsfont-task:before { content: '\e9b6'; } /* '' */
.tsfont-taskperson-s:before { content: '\e9b7'; } /* '' */
.tsfont-taskperson:before { content: '\e9b8'; } /* '' */
.tsfont-team-s:before { content: '\e9b9'; } /* '' */
.tsfont-team:before { content: '\e9ba'; } /* '' */
.tsfont-tencentcloud:before { content: '\e9bc'; } /* '' */
.tsfont-test:before { content: '\e9bd'; } /* '' */
.tsfont-text-delete:before { content: '\e9be'; } /* '' */
.tsfont-textarea:before { content: '\e9bf'; } /* '' */
.tsfont-theme:before { content: '\e9c0'; } /* '' */
.tsfont-tianjiawenjian:before { content: '\e9c1'; } /* '' */
.tsfont-tickets:before { content: '\e9c2'; } /* '' */
.tsfont-time:before { content: '\e9c3'; } /* '' */
.tsfont-timer:before { content: '\e9c4'; } /* '' */
.tsfont-title:before { content: '\e9c5'; } /* '' */
.tsfont-tomcat:before { content: '\e9c6'; } /* '' */
.tsfont-tool:before { content: '\e9c7'; } /* '' */
.tsfont-topo:before { content: '\e9c8'; } /* '' */
.tsfont-transmit:before { content: '\e9c9'; } /* '' */
.tsfont-trash-o:before { content: '\e9ca'; } /* '' */
.tsfont-trash-s:before { content: '\e9cb'; } /* '' */
.tsfont-tree:before { content: '\e9cc'; } /* '' */
.tsfont-tuxedo:before { content: '\e9cd'; } /* '' */
.tsfont-type:before { content: '\e9ce'; } /* '' */
.tsfont-unbind:before { content: '\e9cf'; } /* '' */
.tsfont-undo:before { content: '\e9d0'; } /* '' */
.tsfont-unfold:before { content: '\e9d1'; } /* '' */
.tsfont-unknown:before { content: '\e9d2'; } /* '' */
.tsfont-unlock:before { content: '\e9d3'; } /* '' */
.tsfont-unsend:before { content: '\e9d4'; } /* '' */
.tsfont-up:before { content: '\e9d5'; } /* '' */
.tsfont-upload:before { content: '\e9d6'; } /* '' */
.tsfont-urgency:before { content: '\e9d7'; } /* '' */
.tsfont-user-s:before { content: '\e9d8'; } /* '' */
.tsfont-user-setting:before { content: '\e9d9'; } /* '' */
.tsfont-user:before { content: '\e9da'; } /* '' */
.tsfont-userinfo:before { content: '\e9db'; } /* '' */
.tsfont-vcenter:before { content: '\e9dc'; } /* '' */
.tsfont-verify:before { content: '\e9dd'; } /* '' */
.tsfont-version:before { content: '\e9de'; } /* '' */
.tsfont-vertical-bottom:before { content: '\e9df'; } /* '' */
.tsfont-vertical-middle:before { content: '\e9e0'; } /* '' */
.tsfont-vertical-top:before { content: '\e9e1'; } /* '' */
.tsfont-virtualmachine:before { content: '\e9e2'; } /* '' */
.tsfont-virtualstorage:before { content: '\e9e3'; } /* '' */
.tsfont-vm:before { content: '\e9e4'; } /* '' */
.tsfont-vmware-cluster:before { content: '\e9e5'; } /* '' */
.tsfont-vote-o:before { content: '\e9e6'; } /* '' */
.tsfont-wangluoshujufangxielouxitong:before { content: '\e9e7'; } /* '' */
.tsfont-warning-o:before { content: '\e9e8'; } /* '' */
.tsfont-warning-s:before { content: '\e9e9'; } /* '' */
.tsfont-weblogic:before { content: '\e9ea'; } /* '' */
.tsfont-websphere:before { content: '\e9eb'; } /* '' */
.tsfont-Webyingyongfanghuxitong:before { content: '\e9ec'; } /* '' */
.tsfont-wechat:before { content: '\e9ed'; } /* '' */
.tsfont-wenjian:before { content: '\e9ee'; } /* '' */
.tsfont-windows:before { content: '\e9ef'; } /* '' */
.tsfont-xitongpeizhi:before { content: '\e9f0'; } /* '' */
.tsfont-xunijizhuanyongshebeivpn:before { content: '\e9f1'; } /* '' */
.tsfont-yitihuajigui:before { content: '\e9f2'; } /* '' */
.tsfont-youjiananquanguolvxitong:before { content: '\e9f3'; } /* '' */
.tsfont-yunweishenjishebei:before { content: '\e9f4'; } /* '' */
.tsfont-zhishiku:before { content: '\e9f5'; } /* '' */
.tsfont-zhunrukongzhishebei:before { content: '\e9f6'; } /* '' */
.tsfont-zichanshuruzujian:before { content: '\e9f7'; } /* '' */
.tsfont-zidonghua:before { content: '\e9f8'; } /* '' */
.tsfont-zirenwu:before { content: '\e9f9'; } /* '' */
.tsfont-zookeeper:before { content: '\e9fa'; } /* '' */
.tsfont-storagerpa:before { content: '\e9fb'; } /* '' */
.tsfont-application_module:before { content: '\e9fc'; } /* '' */
.tsfont-dbins:before { content: '\e9fd'; } /* '' */
.tsfont-mssqlserver-db:before { content: '\e9fe'; } /* '' */
.tsfont-sybase-db:before { content: '\e9ff'; } /* '' */
.tsfont-db2-db:before { content: '\ea00'; } /* '' */
.tsfont-oracle-db:before { content: '\ea01'; } /* '' */
.tsfont-postgresql-db:before { content: '\ea02'; } /* '' */
.tsfont-informix-db:before { content: '\ea03'; } /* '' */
.tsfont-mysql-db:before { content: '\ea04'; } /* '' */
.tsfont-k8s_service:before { content: '\ea05'; } /* '' */
.tsfont-k8s_ingress:before { content: '\ea06'; } /* '' */
.tsfont-k8s_deployment:before { content: '\ea07'; } /* '' */
.tsfont-k8s_namespace:before { content: '\ea08'; } /* '' */
.tsfont-k8s_replicaset:before { content: '\ea09'; } /* '' */
.tsfont-k8s_pod:before { content: '\ea0a'; } /* '' */
.tsfont-k8s_node:before { content: '\ea0b'; } /* '' */
.tsfont-zookeepercluster:before { content: '\ea0c'; } /* '' */
.tsfont-vmware-datacenter:before { content: '\ea0d'; } /* '' */
.tsfont-font-bg:before { content: '\ea0e'; } /* '' */
.tsfont-font-color:before { content: '\ea0f'; } /* '' */
.tsfont-pin-angle-o:before { content: '\ea10'; } /* '' */
.tsfont-pin-angle-s:before { content: '\ea11'; } /* '' */
.tsfont-pin-o:before { content: '\ea12'; } /* '' */
.tsfont-pin-s:before { content: '\ea13'; } /* '' */
.tsfont-private-data-source:before { content: '\ea14'; } /* '' */
.tsfont-trigger:before { content: '\ea15'; } /* '' */
.tsfont-webhook:before { content: '\ea16'; } /* '' */
.tsfont-width:before { content: '\ea17'; } /* '' */
.tsfont-neatlogic:before { content: '\ea18'; } /* '' */
.tsfont-gitlab:before { content: '\ea19'; } /* '' */
.tsfont-svn:before { content: '\ea1a'; } /* '' */
.tsfont-merge:before { content: '\ea1b'; } /* '' */
.tsfont-table-column:before { content: '\ea1c'; } /* '' */
.tsfont-table-row:before { content: '\ea1d'; } /* '' */
.tsfont-check-square-o:before { content: '\ea1e'; } /* '' */
.tsfont-double-arrow-left:before { content: '\ea1f'; } /* '' */
.tsfont-double-arrow-right:before { content: '\ea20'; } /* '' */
.tsfont-double-arrow-up:before { content: '\ea21'; } /* '' */
.tsfont-heart-o:before { content: '\ea22'; } /* '' */
.tsfont-folder-open:before { content: '\ea23'; } /* '' */
.tsfont-solid-circle:before { content: '\ea24'; } /* '' */
.tsfont-alert:before { content: '\ea25'; } /* '' */
.tsfont-batch-ops:before { content: '\ea26'; } /* '' */
.tsfont-listsetting:before { content: '\ea27'; } /* '' */
.tsfont-m-apm:before { content: '\ea28'; } /* '' */
.tsfont-m-batchdeploy:before { content: '\ea29'; } /* '' */
.tsfont-m-dashboard-job:before { content: '\ea2a'; } /* '' */
.tsfont-m-dashboard:before { content: '\ea2b'; } /* '' */
.tsfont-m-deployment:before { content: '\ea2c'; } /* '' */
.tsfont-m-ip:before { content: '\ea2d'; } /* '' */
.tsfont-m-octopus:before { content: '\ea2e'; } /* '' */
.tsfont-m-request:before { content: '\ea2f'; } /* '' */
.tsfont-m-signature:before { content: '\ea30'; } /* '' */
.tsfont-m-stack:before { content: '\ea31'; } /* '' */
.tsfont-spinner:before { content: '\ea32'; } /* '' */
.tsfont-tags:before { content: '\ea33'; } /* '' */
.tsfont-border-bottom:before { content: '\ea34'; } /* '' */
.tsfont-border-horizontal:before { content: '\ea35'; } /* '' */
.tsfont-border-left:before { content: '\ea36'; } /* '' */
.tsfont-border-right:before { content: '\ea37'; } /* '' */
.tsfont-border-top:before { content: '\ea38'; } /* '' */
.tsfont-border-vertical:before { content: '\ea39'; } /* '' */
.tsfont-bottom:before { content: '\ea3a'; } /* '' */
.tsfont-double-arrow-down:before { content: '\ea3b'; } /* '' */
.tsfont-top:before { content: '\ea3c'; } /* '' */
.tsfont-ts:before { content: '\ea3d'; } /* '' */
.tsfont-paste:before { content: '\ea3e'; } /* '' */
.tsfont-dataconversion:before { content: '\ea3f'; } /* '' */
.tsfont-rotate-left:before { content: '\ea40'; } /* '' */
.tsfont-scale-to-original:before { content: '\ea41'; } /* '' */
.tsfont-zoom-in:before { content: '\ea42'; } /* '' */
.tsfont-zoom-out:before { content: '\ea43'; } /* '' */
.tsfont-snapshot:before { content: '\ea44'; } /* '' */