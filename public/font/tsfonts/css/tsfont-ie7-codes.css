/* DO NOT EDIT! Generated by iconfount */


.tsfont-a10 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe800;&nbsp;'); }
.tsfont-accessendpoint { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe801;&nbsp;'); }
.tsfont-action { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe802;&nbsp;'); }
.tsfont-activemq { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe803;&nbsp;'); }
.tsfont-adapter { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe804;&nbsp;'); }
.tsfont-addchange { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe805;&nbsp;'); }
.tsfont-addimg-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe806;&nbsp;'); }
.tsfont-addimg { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe807;&nbsp;'); }
.tsfont-addtag { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe808;&nbsp;'); }
.tsfont-addteam { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe809;&nbsp;'); }
.tsfont-adduser { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe80a;&nbsp;'); }
.tsfont-adjust { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe80b;&nbsp;'); }
.tsfont-agent { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe80c;&nbsp;'); }
.tsfont-aix { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe80d;&nbsp;'); }
.tsfont-alicloud { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe80e;&nbsp;'); }
.tsfont-anquangeliwangzha { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe80f;&nbsp;'); }
.tsfont-anquanguanlipingtai { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe810;&nbsp;'); }
.tsfont-anquanwangguan { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe811;&nbsp;'); }
.tsfont-anzhuangbao { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe812;&nbsp;'); }
.tsfont-anzhuangshuxing { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe813;&nbsp;'); }
.tsfont-apache { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe814;&nbsp;'); }
.tsfont-app { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe815;&nbsp;'); }
.tsfont-apple { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe816;&nbsp;'); }
.tsfont-application { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe817;&nbsp;'); }
.tsfont-apps { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe818;&nbsp;'); }
.tsfont-arrow-corner-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe819;&nbsp;'); }
.tsfont-arrow-corner-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe81a;&nbsp;'); }
.tsfont-arrow-down { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe81b;&nbsp;'); }
.tsfont-arrow-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe81c;&nbsp;'); }
.tsfont-arrow-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe81d;&nbsp;'); }
.tsfont-arrow-up { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe81e;&nbsp;'); }
.tsfont-attachment { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe81f;&nbsp;'); }
.tsfont-attribute { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe820;&nbsp;'); }
.tsfont-auth { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe821;&nbsp;'); }
.tsfont-auto { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe822;&nbsp;'); }
.tsfont-aws { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe823;&nbsp;'); }
.tsfont-bad { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe824;&nbsp;'); }
.tsfont-badge { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe825;&nbsp;'); }
.tsfont-baobiao { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe826;&nbsp;'); }
.tsfont-baosong { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe827;&nbsp;'); }
.tsfont-bar { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe828;&nbsp;'); }
.tsfont-barlist { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe829;&nbsp;'); }
.tsfont-batch-download { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe82a;&nbsp;'); }
.tsfont-batch-success { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe82b;&nbsp;'); }
.tsfont-batch-upload { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe82c;&nbsp;'); }
.tsfont-bell-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe82d;&nbsp;'); }
.tsfont-bell-off { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe82e;&nbsp;'); }
.tsfont-bell-on { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe82f;&nbsp;'); }
.tsfont-bell-z { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe830;&nbsp;'); }
.tsfont-bell { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe831;&nbsp;'); }
.tsfont-biaoti { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe832;&nbsp;'); }
.tsfont-bind { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe833;&nbsp;'); }
.tsfont-block { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe834;&nbsp;'); }
.tsfont-blocklist { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe835;&nbsp;'); }
.tsfont-blocks { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe836;&nbsp;'); }
.tsfont-bofenfuyongshebei { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe837;&nbsp;'); }
.tsfont-bold { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe838;&nbsp;'); }
.tsfont-book { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe839;&nbsp;'); }
.tsfont-border-all { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe83a;&nbsp;'); }
.tsfont-border-clear { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe83b;&nbsp;'); }
.tsfont-broom { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe83c;&nbsp;'); }
.tsfont-browser { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe83d;&nbsp;'); }
.tsfont-bushushuxing { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe83e;&nbsp;'); }
.tsfont-calendar { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe83f;&nbsp;'); }
.tsfont-callcenter { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe840;&nbsp;'); }
.tsfont-canvas { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe841;&nbsp;'); }
.tsfont-celve { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe842;&nbsp;'); }
.tsfont-center { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe843;&nbsp;'); }
.tsfont-centos { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe844;&nbsp;'); }
.tsfont-certificate { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe845;&nbsp;'); }
.tsfont-change { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe846;&nbsp;'); }
.tsfont-changing { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe847;&nbsp;'); }
.tsfont-chart-area { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe848;&nbsp;'); }
.tsfont-chart-bar { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe849;&nbsp;'); }
.tsfont-chart-bullet { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe84a;&nbsp;'); }
.tsfont-chart-funnel { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe84b;&nbsp;'); }
.tsfont-chart-gauge { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe84c;&nbsp;'); }
.tsfont-chart-groupedcolumn { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe84d;&nbsp;'); }
.tsfont-chart-heatmap { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe84e;&nbsp;'); }
.tsfont-chart-line { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe84f;&nbsp;'); }
.tsfont-chart-lines { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe850;&nbsp;'); }
.tsfont-chart-liquid { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe851;&nbsp;'); }
.tsfont-chart-number { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe852;&nbsp;'); }
.tsfont-chart-pie { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe853;&nbsp;'); }
.tsfont-chart-polyline { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe854;&nbsp;'); }
.tsfont-chart-progress { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe855;&nbsp;'); }
.tsfont-chart-radar { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe856;&nbsp;'); }
.tsfont-chart-rose { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe857;&nbsp;'); }
.tsfont-chart-sankey { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe858;&nbsp;'); }
.tsfont-chart-scatter { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe859;&nbsp;'); }
.tsfont-chart-scatterbubble { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe85a;&nbsp;'); }
.tsfont-chart-stackedcolumn { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe85b;&nbsp;'); }
.tsfont-chart-table { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe85c;&nbsp;'); }
.tsfont-chart-wordcloud { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe85e;&nbsp;'); }
.tsfont-check-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe85f;&nbsp;'); }
.tsfont-check-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe860;&nbsp;'); }
.tsfont-check-some { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe861;&nbsp;'); }
.tsfont-check-square { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe862;&nbsp;'); }
.tsfont-check { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe863;&nbsp;'); }
.tsfont-checklist { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe864;&nbsp;'); }
.tsfont-ci-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe865;&nbsp;'); }
.tsfont-ci { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe866;&nbsp;'); }
.tsfont-cidaiku { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe867;&nbsp;'); }
.tsfont-cidaizhenlie { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe868;&nbsp;'); }
.tsfont-cientityselect { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe869;&nbsp;'); }
.tsfont-circle-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe86a;&nbsp;'); }
.tsfont-circle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe86b;&nbsp;'); }
.tsfont-circulation-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe86c;&nbsp;'); }
.tsfont-circulation-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe86d;&nbsp;'); }
.tsfont-cisco { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe86e;&nbsp;'); }
.tsfont-close-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe86f;&nbsp;'); }
.tsfont-close-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe870;&nbsp;'); }
.tsfont-close { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe871;&nbsp;'); }
.tsfont-cloud { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe872;&nbsp;'); }
.tsfont-cluster-mode { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe873;&nbsp;'); }
.tsfont-cluster-software { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe874;&nbsp;'); }
.tsfont-cluster { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe875;&nbsp;'); }
.tsfont-code { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe876;&nbsp;'); }
.tsfont-collapse { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe877;&nbsp;'); }
.tsfont-common { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe878;&nbsp;'); }
.tsfont-compare { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe879;&nbsp;'); }
.tsfont-component { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe87a;&nbsp;'); }
.tsfont-config { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe87b;&nbsp;'); }
.tsfont-connector { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe87c;&nbsp;'); }
.tsfont-console { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe87d;&nbsp;'); }
.tsfont-cooperate-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe87e;&nbsp;'); }
.tsfont-cooperate-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe87f;&nbsp;'); }
.tsfont-copy { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe880;&nbsp;'); }
.tsfont-danger-level { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe881;&nbsp;'); }
.tsfont-danger-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe882;&nbsp;'); }
.tsfont-danger-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe883;&nbsp;'); }
.tsfont-datacenter { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe884;&nbsp;'); }
.tsfont-day { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe885;&nbsp;'); }
.tsfont-db-cluster { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe886;&nbsp;'); }
.tsfont-db-ins { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe887;&nbsp;'); }
.tsfont-db { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe888;&nbsp;'); }
.tsfont-db2 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe889;&nbsp;'); }
.tsfont-debug { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe88a;&nbsp;'); }
.tsfont-dell { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe88b;&nbsp;'); }
.tsfont-desktop { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe88c;&nbsp;'); }
.tsfont-devices { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe88d;&nbsp;'); }
.tsfont-dictionary { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe88e;&nbsp;'); }
.tsfont-dns { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe88f;&nbsp;'); }
.tsfont-docker { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe890;&nbsp;'); }
.tsfont-dot { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe891;&nbsp;'); }
.tsfont-down { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe892;&nbsp;'); }
.tsfont-download { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe893;&nbsp;'); }
.tsfont-drafts { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe894;&nbsp;'); }
.tsfont-drag { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe895;&nbsp;'); }
.tsfont-drop-down-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe896;&nbsp;'); }
.tsfont-drop-down { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe897;&nbsp;'); }
.tsfont-drop-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe898;&nbsp;'); }
.tsfont-drop-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe899;&nbsp;'); }
.tsfont-drop-up { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe89a;&nbsp;'); }
.tsfont-duixiangcunchu { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe89b;&nbsp;'); }
.tsfont-edit-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe89c;&nbsp;'); }
.tsfont-edit { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe89d;&nbsp;'); }
.tsfont-elasticsearch { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe89e;&nbsp;'); }
.tsfont-empty { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe89f;&nbsp;'); }
.tsfont-eoa { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a0;&nbsp;'); }
.tsfont-excellent { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a1;&nbsp;'); }
.tsfont-expand { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a2;&nbsp;'); }
.tsfont-export-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a3;&nbsp;'); }
.tsfont-export { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a4;&nbsp;'); }
.tsfont-eye-off { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a5;&nbsp;'); }
.tsfont-eye { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a6;&nbsp;'); }
.tsfont-f5 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a7;&nbsp;'); }
.tsfont-fangbingduwangguan { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a8;&nbsp;'); }
.tsfont-fcdev { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8a9;&nbsp;'); }
.tsfont-fcswitch { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8aa;&nbsp;'); }
.tsfont-file-multi { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ab;&nbsp;'); }
.tsfont-file-single { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ac;&nbsp;'); }
.tsfont-filter-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ad;&nbsp;'); }
.tsfont-filter { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ae;&nbsp;'); }
.tsfont-firewall { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8af;&nbsp;'); }
.tsfont-first { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b0;&nbsp;'); }
.tsfont-flow-children { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b1;&nbsp;'); }
.tsfont-flow-siblings { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b2;&nbsp;'); }
.tsfont-flow { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b3;&nbsp;'); }
.tsfont-fold { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b4;&nbsp;'); }
.tsfont-folder-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b5;&nbsp;'); }
.tsfont-folder-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b6;&nbsp;'); }
.tsfont-folder-share { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b7;&nbsp;'); }
.tsfont-font-size { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b8;&nbsp;'); }
.tsfont-forbid { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8b9;&nbsp;'); }
.tsfont-formcascadelist { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ba;&nbsp;'); }
.tsfont-formdynamiclist { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8bb;&nbsp;'); }
.tsfont-forminput { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8bc;&nbsp;'); }
.tsfont-formlink { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8bd;&nbsp;'); }
.tsfont-formlist { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8be;&nbsp;'); }
.tsfont-formselect { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8bf;&nbsp;'); }
.tsfont-formselectcascader { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8c0;&nbsp;'); }
.tsfont-formstaticlist { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8c1;&nbsp;'); }
.tsfont-formtextarea { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8c2;&nbsp;'); }
.tsfont-formtime { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8c3;&nbsp;'); }
.tsfont-freebsd { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8c4;&nbsp;'); }
.tsfont-fullscreen { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8c5;&nbsp;'); }
.tsfont-fuwuqi { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8c6;&nbsp;'); }
.tsfont-good { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8c7;&nbsp;'); }
.tsfont-group { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8c8;&nbsp;'); }
.tsfont-hadoop { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8c9;&nbsp;'); }
.tsfont-hand { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ca;&nbsp;'); }
.tsfont-hardware { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8cb;&nbsp;'); }
.tsfont-heart-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8cc;&nbsp;'); }
.tsfont-history { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8cd;&nbsp;'); }
.tsfont-home { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ce;&nbsp;'); }
.tsfont-horizontal-center { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8cf;&nbsp;'); }
.tsfont-horizontal-justify { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d0;&nbsp;'); }
.tsfont-horizontal-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d1;&nbsp;'); }
.tsfont-horizontal-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d2;&nbsp;'); }
.tsfont-host { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d3;&nbsp;'); }
.tsfont-hp { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d4;&nbsp;'); }
.tsfont-huawei { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d5;&nbsp;'); }
.tsfont-ibm { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d6;&nbsp;'); }
.tsfont-iis { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d7;&nbsp;'); }
.tsfont-image-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d8;&nbsp;'); }
.tsfont-image { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8d9;&nbsp;'); }
.tsfont-img-center { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8da;&nbsp;'); }
.tsfont-img-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8db;&nbsp;'); }
.tsfont-img-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8dc;&nbsp;'); }
.tsfont-import-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8dd;&nbsp;'); }
.tsfont-import { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8de;&nbsp;'); }
.tsfont-indent { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8df;&nbsp;'); }
.tsfont-info-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e0;&nbsp;'); }
.tsfont-info-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e1;&nbsp;'); }
.tsfont-info { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e2;&nbsp;'); }
.tsfont-informix { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e3;&nbsp;'); }
.tsfont-inlink { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e4;&nbsp;'); }
.tsfont-ins { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e5;&nbsp;'); }
.tsfont-inspection { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e6;&nbsp;'); }
.tsfont-inspur { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e7;&nbsp;'); }
.tsfont-instance { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e8;&nbsp;'); }
.tsfont-integration { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8e9;&nbsp;'); }
.tsfont-internet { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ea;&nbsp;'); }
.tsfont-ip-list { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8eb;&nbsp;'); }
.tsfont-ip-object { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ec;&nbsp;'); }
.tsfont-ipliebiao { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ed;&nbsp;'); }
.tsfont-italic { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ee;&nbsp;'); }
.tsfont-ITfuwu { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ef;&nbsp;'); }
.tsfont-java { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8f0;&nbsp;'); }
.tsfont-jboss { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8f1;&nbsp;'); }
.tsfont-jetty { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8f2;&nbsp;'); }
.tsfont-jiamishebei { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8f3;&nbsp;'); }
.tsfont-jicheng { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8f4;&nbsp;'); }
.tsfont-jifang { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8f5;&nbsp;'); }
.tsfont-json { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8f6;&nbsp;'); }
.tsfont-juniper { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8f7;&nbsp;'); }
.tsfont-k8s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8f8;&nbsp;'); }
.tsfont-kafka { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8f9;&nbsp;'); }
.tsfont-kangjujuefuwushebei { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8fa;&nbsp;'); }
.tsfont-keepalive { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8fb;&nbsp;'); }
.tsfont-label-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8fc;&nbsp;'); }
.tsfont-label { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8fd;&nbsp;'); }
.tsfont-last { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8fe;&nbsp;'); }
.tsfont-layer { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe8ff;&nbsp;'); }
.tsfont-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe900;&nbsp;'); }
.tsfont-lightning { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe901;&nbsp;'); }
.tsfont-lighttpd { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe902;&nbsp;'); }
.tsfont-linux { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe903;&nbsp;'); }
.tsfont-list { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe904;&nbsp;'); }
.tsfont-liuliangfenxishebei { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe905;&nbsp;'); }
.tsfont-loadblance-vs { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe906;&nbsp;'); }
.tsfont-location-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe907;&nbsp;'); }
.tsfont-location { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe908;&nbsp;'); }
.tsfont-lock-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe909;&nbsp;'); }
.tsfont-lock { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe90a;&nbsp;'); }
.tsfont-loudongsaomiaoshebei { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe90b;&nbsp;'); }
.tsfont-lun { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe90c;&nbsp;'); }
.tsfont-mail-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe90d;&nbsp;'); }
.tsfont-mail-read-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe90e;&nbsp;'); }
.tsfont-mail-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe90f;&nbsp;'); }
.tsfont-mail-unread-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe910;&nbsp;'); }
.tsfont-manager { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe911;&nbsp;'); }
.tsfont-mark-all { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe912;&nbsp;'); }
.tsfont-memcached { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe913;&nbsp;'); }
.tsfont-mesos { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe914;&nbsp;'); }
.tsfont-message-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe915;&nbsp;'); }
.tsfont-message { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe916;&nbsp;'); }
.tsfont-minus-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe917;&nbsp;'); }
.tsfont-minus-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe918;&nbsp;'); }
.tsfont-minus-square { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe919;&nbsp;'); }
.tsfont-minus { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe91a;&nbsp;'); }
.tsfont-mm-bat { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe91b;&nbsp;'); }
.tsfont-mm-bmp { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe91c;&nbsp;'); }
.tsfont-mm-cls { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe91d;&nbsp;'); }
.tsfont-mm-cmd { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe91e;&nbsp;'); }
.tsfont-mm-cnf { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe91f;&nbsp;'); }
.tsfont-mm-css { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe920;&nbsp;'); }
.tsfont-mm-dir { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe921;&nbsp;'); }
.tsfont-mm-doc { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe922;&nbsp;'); }
.tsfont-mm-docx { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe923;&nbsp;'); }
.tsfont-mm-exe { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe924;&nbsp;'); }
.tsfont-mm-gif { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe925;&nbsp;'); }
.tsfont-mm-gzip { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe926;&nbsp;'); }
.tsfont-mm-html { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe927;&nbsp;'); }
.tsfont-mm-java { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe928;&nbsp;'); }
.tsfont-mm-jpeg { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe929;&nbsp;'); }
.tsfont-mm-jpg { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe92a;&nbsp;'); }
.tsfont-mm-js { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe92b;&nbsp;'); }
.tsfont-mm-misc { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe92c;&nbsp;'); }
.tsfont-mm-mov { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe92d;&nbsp;'); }
.tsfont-mm-mp4 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe92e;&nbsp;'); }
.tsfont-mm-png { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe92f;&nbsp;'); }
.tsfont-mm-ppt { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe930;&nbsp;'); }
.tsfont-mm-py { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe931;&nbsp;'); }
.tsfont-mm-rar { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe932;&nbsp;'); }
.tsfont-mm-rpm { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe933;&nbsp;'); }
.tsfont-mm-rtf { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe934;&nbsp;'); }
.tsfont-mm-sh { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe935;&nbsp;'); }
.tsfont-mm-sql { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe936;&nbsp;'); }
.tsfont-mm-svg { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe937;&nbsp;'); }
.tsfont-mm-tar { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe938;&nbsp;'); }
.tsfont-mm-txt { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe939;&nbsp;'); }
.tsfont-mm-unknown { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe93a;&nbsp;'); }
.tsfont-mm-vbs { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe93b;&nbsp;'); }
.tsfont-mm-xls { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe93c;&nbsp;'); }
.tsfont-mm-xlsm { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe93d;&nbsp;'); }
.tsfont-mm-xlsx { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe93e;&nbsp;'); }
.tsfont-mm-xml { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe93f;&nbsp;'); }
.tsfont-mm-zip { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe940;&nbsp;'); }
.tsfont-module { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe941;&nbsp;'); }
.tsfont-modules { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe942;&nbsp;'); }
.tsfont-mongodb { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe943;&nbsp;'); }
.tsfont-monitor { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe944;&nbsp;'); }
.tsfont-mssqlserver { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe945;&nbsp;'); }
.tsfont-mysql { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe946;&nbsp;'); }
.tsfont-net-area { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe947;&nbsp;'); }
.tsfont-netarea { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe948;&nbsp;'); }
.tsfont-netdev { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe949;&nbsp;'); }
.tsfont-nginx { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe94a;&nbsp;'); }
.tsfont-night { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe94b;&nbsp;'); }
.tsfont-node { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe94c;&nbsp;'); }
.tsfont-node1 { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe94d;&nbsp;'); }
.tsfont-non-auth { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe94e;&nbsp;'); }
.tsfont-novmtool { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe94f;&nbsp;'); }
.tsfont-nutanix { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe950;&nbsp;'); }
.tsfont-off-fullscreen { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe951;&nbsp;'); }
.tsfont-openstack { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe952;&nbsp;'); }
.tsfont-option-horizontal { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe953;&nbsp;'); }
.tsfont-option-vertical { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe954;&nbsp;'); }
.tsfont-oracle-rac { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe955;&nbsp;'); }
.tsfont-oracle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe956;&nbsp;'); }
.tsfont-orderlist { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe957;&nbsp;'); }
.tsfont-ordinary { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe958;&nbsp;'); }
.tsfont-os-cluster { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe959;&nbsp;'); }
.tsfont-os { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe95a;&nbsp;'); }
.tsfont-outdent { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe95b;&nbsp;'); }
.tsfont-outline-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe95c;&nbsp;'); }
.tsfont-palette { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe95d;&nbsp;'); }
.tsfont-pause-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe95e;&nbsp;'); }
.tsfont-pause-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe95f;&nbsp;'); }
.tsfont-pause { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe960;&nbsp;'); }
.tsfont-peizhiguanli { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe961;&nbsp;'); }
.tsfont-peizhihechashebei { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe962;&nbsp;'); }
.tsfont-permission { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe963;&nbsp;'); }
.tsfont-phone { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe964;&nbsp;'); }
.tsfont-php { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe965;&nbsp;'); }
.tsfont-pie { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe966;&nbsp;'); }
.tsfont-pingbijigui { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe967;&nbsp;'); }
.tsfont-play-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe968;&nbsp;'); }
.tsfont-play-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe969;&nbsp;'); }
.tsfont-play { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe96a;&nbsp;'); }
.tsfont-plugin { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe96b;&nbsp;'); }
.tsfont-plus-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe96c;&nbsp;'); }
.tsfont-plus-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe96d;&nbsp;'); }
.tsfont-plus-square { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe96e;&nbsp;'); }
.tsfont-plus { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe96f;&nbsp;'); }
.tsfont-pod { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe970;&nbsp;'); }
.tsfont-port { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe971;&nbsp;'); }
.tsfont-postgresql { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe972;&nbsp;'); }
.tsfont-proxy { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe973;&nbsp;'); }
.tsfont-pulse { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe974;&nbsp;'); }
.tsfont-putongjigui { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe975;&nbsp;'); }
.tsfont-python { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe976;&nbsp;'); }
.tsfont-question-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe977;&nbsp;'); }
.tsfont-question-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe978;&nbsp;'); }
.tsfont-question { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe979;&nbsp;'); }
.tsfont-rabbitmq { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe97a;&nbsp;'); }
.tsfont-redhat { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe97b;&nbsp;'); }
.tsfont-redis { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe97c;&nbsp;'); }
.tsfont-refresh { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe97d;&nbsp;'); }
.tsfont-reminder { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe97e;&nbsp;'); }
.tsfont-reply-all { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe97f;&nbsp;'); }
.tsfont-reply { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe980;&nbsp;'); }
.tsfont-report { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe981;&nbsp;'); }
.tsfont-resin { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe982;&nbsp;'); }
.tsfont-restart { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe983;&nbsp;'); }
.tsfont-restful { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe984;&nbsp;'); }
.tsfont-revover { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe985;&nbsp;'); }
.tsfont-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe986;&nbsp;'); }
.tsfont-riqishijian { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe987;&nbsp;'); }
.tsfont-rizhishoujiyufenxixitong { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe988;&nbsp;'); }
.tsfont-role-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe989;&nbsp;'); }
.tsfont-role { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe98a;&nbsp;'); }
.tsfont-rotate-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe98b;&nbsp;'); }
.tsfont-router { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe98c;&nbsp;'); }
.tsfont-run { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe98d;&nbsp;'); }
.tsfont-ruqinjianceyufangyushebei { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe98e;&nbsp;'); }
.tsfont-save { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe98f;&nbsp;'); }
.tsfont-scene { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe990;&nbsp;'); }
.tsfont-script { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe991;&nbsp;'); }
.tsfont-search-minus { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe992;&nbsp;'); }
.tsfont-search-plus { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe993;&nbsp;'); }
.tsfont-search { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe994;&nbsp;'); }
.tsfont-secdev { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe995;&nbsp;'); }
.tsfont-security { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe996;&nbsp;'); }
.tsfont-send { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe997;&nbsp;'); }
.tsfont-setting { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe998;&nbsp;'); }
.tsfont-shangwanghangweiguanlishebei { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe999;&nbsp;'); }
.tsfont-share { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe99a;&nbsp;'); }
.tsfont-shitu { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe99b;&nbsp;'); }
.tsfont-shujukutuomin { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe99c;&nbsp;'); }
.tsfont-shunt { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe99d;&nbsp;'); }
.tsfont-shuziqianmingshebei { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe99e;&nbsp;'); }
.tsfont-sla { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe99f;&nbsp;'); }
.tsfont-softwareservice { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a0;&nbsp;'); }
.tsfont-sop { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a1;&nbsp;'); }
.tsfont-sort-all { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a2;&nbsp;'); }
.tsfont-sort-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a3;&nbsp;'); }
.tsfont-sort-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a4;&nbsp;'); }
.tsfont-spacing { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a5;&nbsp;'); }
.tsfont-spark { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a6;&nbsp;'); }
.tsfont-square { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a7;&nbsp;'); }
.tsfont-stage { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a8;&nbsp;'); }
.tsfont-star-border { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9a9;&nbsp;'); }
.tsfont-star-half { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9aa;&nbsp;'); }
.tsfont-star { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ab;&nbsp;'); }
.tsfont-stars { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ac;&nbsp;'); }
.tsfont-storage { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ad;&nbsp;'); }
.tsfont-storages { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ae;&nbsp;'); }
.tsfont-storm { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9af;&nbsp;'); }
.tsfont-switch { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b0;&nbsp;'); }
.tsfont-sybase { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b1;&nbsp;'); }
.tsfont-tabforword { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b2;&nbsp;'); }
.tsfont-takeover { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b3;&nbsp;'); }
.tsfont-task-cancel { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b4;&nbsp;'); }
.tsfont-task-ok { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b5;&nbsp;'); }
.tsfont-task { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b6;&nbsp;'); }
.tsfont-taskperson-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b7;&nbsp;'); }
.tsfont-taskperson { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b8;&nbsp;'); }
.tsfont-team-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9b9;&nbsp;'); }
.tsfont-team { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ba;&nbsp;'); }
.tsfont-tencentcloud { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9bc;&nbsp;'); }
.tsfont-test { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9bd;&nbsp;'); }
.tsfont-text-delete { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9be;&nbsp;'); }
.tsfont-textarea { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9bf;&nbsp;'); }
.tsfont-theme { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c0;&nbsp;'); }
.tsfont-tianjiawenjian { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c1;&nbsp;'); }
.tsfont-tickets { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c2;&nbsp;'); }
.tsfont-time { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c3;&nbsp;'); }
.tsfont-timer { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c4;&nbsp;'); }
.tsfont-title { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c5;&nbsp;'); }
.tsfont-tomcat { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c6;&nbsp;'); }
.tsfont-tool { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c7;&nbsp;'); }
.tsfont-topo { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c8;&nbsp;'); }
.tsfont-transmit { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9c9;&nbsp;'); }
.tsfont-trash-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ca;&nbsp;'); }
.tsfont-trash-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9cb;&nbsp;'); }
.tsfont-tree { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9cc;&nbsp;'); }
.tsfont-tuxedo { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9cd;&nbsp;'); }
.tsfont-type { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ce;&nbsp;'); }
.tsfont-unbind { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9cf;&nbsp;'); }
.tsfont-undo { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9d0;&nbsp;'); }
.tsfont-unfold { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9d1;&nbsp;'); }
.tsfont-unknown { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9d2;&nbsp;'); }
.tsfont-unlock { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9d3;&nbsp;'); }
.tsfont-unsend { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9d4;&nbsp;'); }
.tsfont-up { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9d5;&nbsp;'); }
.tsfont-upload { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9d6;&nbsp;'); }
.tsfont-urgency { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9d7;&nbsp;'); }
.tsfont-user-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9d8;&nbsp;'); }
.tsfont-user-setting { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9d9;&nbsp;'); }
.tsfont-user { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9da;&nbsp;'); }
.tsfont-userinfo { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9db;&nbsp;'); }
.tsfont-vcenter { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9dc;&nbsp;'); }
.tsfont-verify { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9dd;&nbsp;'); }
.tsfont-version { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9de;&nbsp;'); }
.tsfont-vertical-bottom { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9df;&nbsp;'); }
.tsfont-vertical-middle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e0;&nbsp;'); }
.tsfont-vertical-top { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e1;&nbsp;'); }
.tsfont-virtualmachine { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e2;&nbsp;'); }
.tsfont-virtualstorage { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e3;&nbsp;'); }
.tsfont-vm { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e4;&nbsp;'); }
.tsfont-vmware-cluster { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e5;&nbsp;'); }
.tsfont-vote-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e6;&nbsp;'); }
.tsfont-wangluoshujufangxielouxitong { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e7;&nbsp;'); }
.tsfont-warning-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e8;&nbsp;'); }
.tsfont-warning-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9e9;&nbsp;'); }
.tsfont-weblogic { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ea;&nbsp;'); }
.tsfont-websphere { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9eb;&nbsp;'); }
.tsfont-Webyingyongfanghuxitong { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ec;&nbsp;'); }
.tsfont-wechat { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ed;&nbsp;'); }
.tsfont-wenjian { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ee;&nbsp;'); }
.tsfont-windows { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ef;&nbsp;'); }
.tsfont-xitongpeizhi { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9f0;&nbsp;'); }
.tsfont-xunijizhuanyongshebeivpn { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9f1;&nbsp;'); }
.tsfont-yitihuajigui { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9f2;&nbsp;'); }
.tsfont-youjiananquanguolvxitong { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9f3;&nbsp;'); }
.tsfont-yunweishenjishebei { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9f4;&nbsp;'); }
.tsfont-zhishiku { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9f5;&nbsp;'); }
.tsfont-zhunrukongzhishebei { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9f6;&nbsp;'); }
.tsfont-zichanshuruzujian { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9f7;&nbsp;'); }
.tsfont-zidonghua { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9f8;&nbsp;'); }
.tsfont-zirenwu { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9f9;&nbsp;'); }
.tsfont-zookeeper { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9fa;&nbsp;'); }
.tsfont-storagerpa { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9fb;&nbsp;'); }
.tsfont-application_module { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9fc;&nbsp;'); }
.tsfont-dbins { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9fd;&nbsp;'); }
.tsfont-mssqlserver-db { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9fe;&nbsp;'); }
.tsfont-sybase-db { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xe9ff;&nbsp;'); }
.tsfont-db2-db { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea00;&nbsp;'); }
.tsfont-oracle-db { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea01;&nbsp;'); }
.tsfont-postgresql-db { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea02;&nbsp;'); }
.tsfont-informix-db { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea03;&nbsp;'); }
.tsfont-mysql-db { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea04;&nbsp;'); }
.tsfont-k8s_service { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea05;&nbsp;'); }
.tsfont-k8s_ingress { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea06;&nbsp;'); }
.tsfont-k8s_deployment { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea07;&nbsp;'); }
.tsfont-k8s_namespace { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea08;&nbsp;'); }
.tsfont-k8s_replicaset { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea09;&nbsp;'); }
.tsfont-k8s_pod { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea0a;&nbsp;'); }
.tsfont-k8s_node { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea0b;&nbsp;'); }
.tsfont-zookeepercluster { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea0c;&nbsp;'); }
.tsfont-vmware-datacenter { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea0d;&nbsp;'); }
.tsfont-font-bg { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea0e;&nbsp;'); }
.tsfont-font-color { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea0f;&nbsp;'); }
.tsfont-pin-angle-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea10;&nbsp;'); }
.tsfont-pin-angle-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea11;&nbsp;'); }
.tsfont-pin-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea12;&nbsp;'); }
.tsfont-pin-s { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea13;&nbsp;'); }
.tsfont-private-data-source { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea14;&nbsp;'); }
.tsfont-trigger { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea15;&nbsp;'); }
.tsfont-webhook { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea16;&nbsp;'); }
.tsfont-width { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea17;&nbsp;'); }
.tsfont-neatlogic { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea18;&nbsp;'); }
.tsfont-gitlab { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea19;&nbsp;'); }
.tsfont-svn { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea1a;&nbsp;'); }
.tsfont-merge { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea1b;&nbsp;'); }
.tsfont-table-column { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea1c;&nbsp;'); }
.tsfont-table-row { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea1d;&nbsp;'); }
.tsfont-check-square-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea1e;&nbsp;'); }
.tsfont-double-arrow-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea1f;&nbsp;'); }
.tsfont-double-arrow-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea20;&nbsp;'); }
.tsfont-double-arrow-up { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea21;&nbsp;'); }
.tsfont-heart-o { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea22;&nbsp;'); }
.tsfont-folder-open { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea23;&nbsp;'); }
.tsfont-solid-circle { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea24;&nbsp;'); }
.tsfont-alert { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea25;&nbsp;'); }
.tsfont-batch-ops { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea26;&nbsp;'); }
.tsfont-listsetting { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea27;&nbsp;'); }
.tsfont-m-apm { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea28;&nbsp;'); }
.tsfont-m-batchdeploy { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea29;&nbsp;'); }
.tsfont-m-dashboard-job { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea2a;&nbsp;'); }
.tsfont-m-dashboard { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea2b;&nbsp;'); }
.tsfont-m-deployment { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea2c;&nbsp;'); }
.tsfont-m-ip { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea2d;&nbsp;'); }
.tsfont-m-octopus { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea2e;&nbsp;'); }
.tsfont-m-request { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea2f;&nbsp;'); }
.tsfont-m-signature { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea30;&nbsp;'); }
.tsfont-m-stack { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea31;&nbsp;'); }
.tsfont-spinner { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea32;&nbsp;'); }
.tsfont-tags { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea33;&nbsp;'); }
.tsfont-border-bottom { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea34;&nbsp;'); }
.tsfont-border-horizontal { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea35;&nbsp;'); }
.tsfont-border-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea36;&nbsp;'); }
.tsfont-border-right { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea37;&nbsp;'); }
.tsfont-border-top { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea38;&nbsp;'); }
.tsfont-border-vertical { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea39;&nbsp;'); }
.tsfont-bottom { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea3a;&nbsp;'); }
.tsfont-double-arrow-down { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea3b;&nbsp;'); }
.tsfont-top { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea3c;&nbsp;'); }
.tsfont-ts { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea3d;&nbsp;'); }
.tsfont-paste { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea3e;&nbsp;'); }
.tsfont-dataconversion { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea3f;&nbsp;'); }
.tsfont-rotate-left { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea40;&nbsp;'); }
.tsfont-scale-to-original { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea41;&nbsp;'); }
.tsfont-zoom-in { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea42;&nbsp;'); }
.tsfont-zoom-out { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea43;&nbsp;'); }
.tsfont-snapshot { *zoom: expression( this.runtimeStyle['zoom'] = '1', this.innerHTML = '&#xea44;&nbsp;'); }