/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        brand: 'var(--color-brand)',
        't-primary': 'var(--color-t-primary)',
        't-second': 'var(--color-t-second)',
        't-description': 'var(--color-t-description)',
        divider: '#F0F0Fvar(--color-divider)',
        'page-bg': 'var(--color-page-bg)',
        success: 'var(--color-success)',
        warning: 'var(--color-warning)',
        error: 'var(--color-error)',
        info: 'var(--color-info)',
      }
    },
  },
  plugins: [],
}
