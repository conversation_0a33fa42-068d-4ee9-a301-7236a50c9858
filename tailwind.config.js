/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        brand: 'var(--color-brand)',
        't-primary': 'var(--color-t-primary)',
        't-secont': 'var(--color-t-secont)',
        't-description': 'var(--color-t-description)',
        divider: '#F0F0Fvar(--color-divider)',
        'page-bg': 'var(--color-page-bg)',
        success: 'var(--color-success)',
        warning: 'var(--color-warning)',
        error: 'var(--color-error)',
        info: 'var(--color-info)',
      }
    },
  },
  plugins: [],
}


/*
@theme {
  --color-brand: #2F54D4;
  --color-t-primary: #333;
  --color-t-secont: #666;
  --color-t-description: #999;
  --color-divider: #F0F0F0; 
  --color-page-bg: #F4F4F4;
  --color-success: #01EE87;
  --color-warning: #ff9900;
  --color-error: #ff2900;
  --color-info: #f4f4f4;
}

*/