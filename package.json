{"name": "neatlogic-mobile", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"antd-mobile": "^5.39.0", "axios": "^1.11.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "dingtalk-jsapi": "^3.1.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router": "^7.7.1", "sonner": "^2.0.6", "swr": "^2.3.4", "vconsole": "^3.15.1", "zustand": "^5.0.6"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/crypto-js": "^4.2.2", "@types/node": "^22.16.5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.21", "babel-plugin-import": "^1.13.8", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.3", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4"}}