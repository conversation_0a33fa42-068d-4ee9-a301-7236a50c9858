function _0x1b1c(_0x47f62c, _0x8e6272) {
	const _0x36bc35 = _0x36bc();
	return (
		(_0x1b1c = function (_0x1b1cdd, _0x56500e) {
			_0x1b1cdd = _0x1b1cdd - 0x102;
			let _0x3f2802 = _0x36bc35[_0x1b1cdd];
			return _0x3f2802;
		}),
		_0x1b1c(_0x47f62c, _0x8e6272)
	);
}
const _0x23d6cc = _0x1b1c;
(function (_0x4a728c, _0x3bc7f7) {
	const _0x46565e = _0x1b1c,
		_0x258e37 = _0x4a728c();
	while (!![]) {
		try {
			const _0x4a67ec =
				(parseInt(_0x46565e(0x10d)) / 0x1) * (-parseInt(_0x46565e(0x108)) / 0x2) +
				(-parseInt(_0x46565e(0x107)) / 0x3) * (parseInt(_0x46565e(0x109)) / 0x4) +
				(parseInt(_0x46565e(0x104)) / 0x5) * (-parseInt(_0x46565e(0x106)) / 0x6) +
				-parseInt(_0x46565e(0x10c)) / 0x7 +
				parseInt(_0x46565e(0x103)) / 0x8 +
				-parseInt(_0x46565e(0x102)) / 0x9 +
				(parseInt(_0x46565e(0x105)) / 0xa) * (parseInt(_0x46565e(0x10a)) / 0xb);
			if (_0x4a67ec === _0x3bc7f7) break;
			else _0x258e37["push"](_0x258e37["shift"]());
		} catch (_0x17e832) {
			_0x258e37["push"](_0x258e37["shift"]());
		}
	}
})(_0x36bc, 0xc21ce);

function _0x36bc() {
	const _0x3597f1 = [
		"7045479gmRBhC",
		"17293WZXDah",
		"4232862OufEBv",
		"2159488dBhILi",
		"145drIIXL",
		"710imYqBm",
		"74316vMiJHq",
		"168321Jhjtyk",
		"24yDdfFy",
		"8ZmmWdu",
		"415349iywdzH",
		[+!+[]] +
			[!+[] + !+[]] +
			[!+[] + !+[] + !+[]] +
			[!+[] + !+[] + !+[] + !+[]] +
			[!+[] + !+[] + !+[] + !+[] + !+[]] +
			[!+[] + !+[] + !+[] + !+[] + !+[] + !+[]] +
			[!+[] + !+[] + !+[] + !+[] + !+[] + !+[] + !+[]] +
			[!+[] + !+[] + !+[] + !+[] + !+[] + !+[] + !+[] + !+[]] +
			[!+[] + !+[] + !+[] + !+[] + !+[] + !+[] + !+[] + !+[] + !+[]] +
			[+[]] +
			[+[]] +
			[+[]] +
			[+[]] +
			[+[]] +
			[+[]] +
			[+[]],
	];
	_0x36bc = function () {
		return _0x3597f1;
	};
	return _0x36bc();
}

const a = _0x23d6cc(0x10b);

export default a;
