import type { SSOAppType } from "#/entity/auth";
import dayjs from "dayjs";

export function parseUrlToObj(url: string): Record<string, any> {
	if (!url) {
		return {};
	}
	const res: Record<string, string | string[]> = {};
	const [, paramsStr] = url.split("?");
	if (paramsStr) {
		const kvArr = paramsStr.split("&");
		for (const kvStr of kvArr) {
			const [k, v] = kvStr.split("=");
			if (res[k]) {
				if (Array.isArray(res[k])) {
					(res[k] as string[]).push(v);
				} else {
					res[k] = [res[k], v] as string[];
				}
			} else {
				res[k] = v;
			}
		}
	}
	return res;
}

export function JsonParse<R = any>(str: string) {
	try {
		const res = JSON.parse(str) as R;
		return res;
	} catch (error) {
		console.error("JSON解析错误, 您正在试图解析 ", str.toString());
		return undefined;
	}
}

// 加密身份证号中间的内容，只保留前三位和后两位，中间使用*代替
export function encryptIdCard(idCard?: string) {
	if (!idCard || idCard.length !== 18) {
		return idCard;
	}
	return `${idCard.slice(0, 3)}${'*'.repeat(12)}${idCard.slice(-2)}`;
}

// 加密手机号中间的内容，只保留前三位和后四位，中间使用*代替
export function encryptPhone(phone?: string) {
	if (!phone || phone.length !== 11) {
		return phone;
	}
	return `${phone.slice(0, 3)}${'*'.repeat(4)}${phone.slice(-4)}`;
}

// 格式化时间：传入内容为：2025-05-13T13:59:51.620085+08:00，返回：2025-05-13 13:59:51
export function formatTime(time?: string) {
	if (!time) {
		return time;
	}
	return dayjs(time).format("YYYY-MM-DD HH:mm:ss");
}

export function getSSOAppTypeFromUa(): SSOAppType {
  const ua = navigator.userAgent.toLowerCase();
  if (ua.indexOf('wxwork') !== -1) {
    return 'WEIXINWORK'
  }if (ua.indexOf('micromessenger') !== -1) {
    return 'WEIXINMP'
  } if (ua.indexOf('dingtalk') !== -1) {
    return 'DINGTALK'
  }  if (ua.indexOf('huawei-anyoffice') !== -1 || ua.indexOf('wecodeide') !== -1 || ua.indexOf('welink') !== -1) {
    return 'WELINK'
  }
  return 'WEB'
}