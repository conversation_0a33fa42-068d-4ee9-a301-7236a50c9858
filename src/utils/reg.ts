const regs = {
	onlyNumber: /^\d+$/, // 仅数字
	positiveInteger: /^[1-9]\d*$/, // 正整数
	notNegativeInterger: /^[1-9]\d*|0$/, //非负整数
	onlyChinese: /[\u4e00-\u9fa5]/gm, // 仅汉字
	onlyEnglish: /^[a-z]+$/i, // 仅英文字母
	onlyLowerCase: /^[a-z]+$/, // 仅小写英文字母
	onlyUpperCase: /^[A-Z]+$/, // 仅大写英文字母
	onlyEnglishAndNumber: /^[a-z0-9]+$/i, // 仅英文字母和数字
	noSpace: /^[^\s]*$/, //不能有空格
	email: /\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/, // 邮箱地址
	url: /^https?:\/\/(([a-zA-Z0-9_-])+(\.)?)*(:\d+)?(\/((\.)?(\?)?=?&?[a-zA-Z0-9_-](\?)?)*)*$/i, // url地址
	phoneStrict: /^(0|86|17951)?(13[0-9]|15[012356789]|166|17[3678]|18[0-9]|14[5789]|19[0-9])[0-9]{8}$/,
	phone: /^1[3-9]\d{9}$/,
	idNumber:
		/^(^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$)|(^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])((\d{4})|\d{3}[Xx])$)$/, // 身份证号
	date: /^[1-2][0-9][0-9][0-9]-[0-1]{0,1}[0-9]-[0-3]{0,1}[0-9]$/, // 日期 yyyy-MM-DD
	concurrencyReg: /^[1-9]\d*$|^([1-9]\d*)([+])([1-9]\d*)$/, // 并发数: x+x
	ip: /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])(\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])|\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\/([1-9]|[1-2]\d|3[0-1]))$/, // ip地址
	mac: /^([0-9a-fA-F]{2})(([/\s:-][0-9a-fA-F]{2}){5})$/, // mac地址
	port: /^([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/, // 端口正则
	onlyEnglishAndNumberAndChinese: /^[\u4E00-\u9FA5A-Za-z0-9]+$/, // 仅支持大小写英文字母、数字、汉字
	onlyEnglishAndUnderline: /^[a-z_]+$/i, // 仅英文字母和数字
};

export default regs;
