import { getSSOAppTypeFromUa } from '@/utils';
import { useNavigate, useSearchParams } from 'react-router'
import type { SSOAppType } from '#/entity/auth'
import { useAuthStore } from '@/store/authStore';
import { useCallback, useEffect, useState, useMemo } from 'react';
import * as dd from 'dingtalk-jsapi';

interface SSOCallbackParams {
  code?: string;
  ticket?: string;
  state?: string;
  p?: string; // bas 的加密传参
  appType?: SSOAppType;
  ou?: string;
  corpId?: string; // 钉钉获取 code 需要使用
}

const getWelinkCode = (retryCount = 1): Promise<string> => new Promise((resolve, reject) => {
  const handleRetry = () => {
    if (retryCount > 0) {
      return getWelinkCode(retryCount - 1).then(resolve).catch(reject);
    }
    reject(new Error('获取code失败'));
  };

  window.HWH5?.getAuthCode().then((data: any) => {
    const code = data?.code as string;
    if (code && code !== 'error') {
      resolve(code);
    } else {
      console.log('获取code失败,sdk返回为', data);
      handleRetry();
    }
  }).catch(handleRetry);
});

const getDingtalkCode = (corpId: string): Promise<string> => {

  return new Promise((resolve, reject) => {
    dd.getAuthCode({
      corpId,
      success: (res: any) => {
        const code = res.code as string;
        resolve(code)
      },
      fail: (err: any) => {
        reject(err)
      },
    });
  })
}


function useSSO() {
  const [searchParams] = useSearchParams();
  const { checkAuth } = useAuthStore()
  const isAuth = checkAuth();
  const [ssoId, setSSOId] = useState('');
  const navigate = useNavigate();

  // 使用 useMemo 来避免 params 对象每次都重新创建
  const params = useMemo(() => {
    return Object.fromEntries(searchParams.entries()) as SSOCallbackParams;
  }, [searchParams]);

  const ssoAppType = params.appType || getSSOAppTypeFromUa()
  const ssoCodeFromUrl = params.code || params.ticket
  const ou = params.ou;

  const gotoSSO = useCallback(async (ssoAppType: SSOAppType) => {
    switch(ssoAppType) {
        case 'WEIXINMP': {
          const appId = import.meta.env.VITE_APP_WECHAT_OA_APPID
          const wechatRedirect = encodeURIComponent(window.location.href)
          window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${wechatRedirect}&response_type=code&scope=snsapi_base#wechat_redirect`;
          break;
        }
        case 'WEIXINWORK': {
          const appId = import.meta.env.VITE_APP_WECHAT_OA_APPID
          const wechatRedirect = encodeURIComponent(window.location.href)
          window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${wechatRedirect}&response_type=code&scope=snsapi_base&connect_redirect=1&state=#wechat_redirect`;
          break;
        }
        case 'WELINK': {
          const code = await getWelinkCode()
          if (code) {
            setSSOId(code)
          }
          break;
        }
        case 'DINGTALK': {
          const corpId = params.corpId;
          if (corpId) {
            const code = await getDingtalkCode(corpId);
            if (code) {
              setSSOId(code)
            }
          }
          break;
        }
        default: {
          break
        }
      }
  }, [params.corpId])

  useEffect(() => {
    if (!isAuth && !ssoCodeFromUrl) {
      // 未登录且没有回调
      gotoSSO(ssoAppType)
      return
    }
    if (!isAuth && ssoCodeFromUrl) {
      // 未登录且有回调参数
      //通过 ssoCode 换取用户身份，如果是微信，需要先获取 openid
      setSSOId(ssoCodeFromUrl)
    }
    if (isAuth) {
      // 已登录
      navigate('/')
    }
  }, [isAuth, ssoCodeFromUrl, ssoAppType, navigate, gotoSSO])
  return {
    id: ssoId,
    type: ssoAppType,
    ou
  }
}

export default useSSO