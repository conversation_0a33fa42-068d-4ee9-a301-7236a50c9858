import type { PopupProps} from 'antd-mobile'
import { useCallback, useState } from 'react';

interface UsePopupReturn {
  popupProps: PopupProps,
  open: () => void,
  close: () => void
}


function usePopup(): UsePopupReturn {
  const [visible, setVisible] = useState(false);
  const open = useCallback(() => {
    setVisible(true)
  }, [])
  const close = useCallback(()  => {
    setVisible(false)
  }, [])
  return {
    popupProps: {
      visible,
      position: 'bottom',
      bodyStyle: {
        height: '40vh'
      },
      onMaskClick: close,
      onClose: close
    },
    open,
      close
  }
}

export default usePopup;