import useNLRequest from '@/hooks/use-nl-request';
import type { ProcessTaskDraft } from '#/entity/process';
import { useMemo } from 'react';
import type { NLFormComponent } from '#/entity/process/nl-form-components';

export default function useNLProcessFormdata(channelUuid?: string) {
  const {data, isLoading, mutate} = useNLRequest<ProcessTaskDraft>(channelUuid ? ['/api/rest/processtask/draft/get', {channelUuid }] : null, {
    revalidateOnFocus: false,
  })
  const usefullInfo = useMemo(() => {
    const channelPath = data?.channelPath;
    const channelType = data?.channelType
    const channelVo = data?.channelVo;
    const tableList = data?.formConfig?.tableList || [];
    const headerData = {
      channelPath,
      color: channelType?.color,
      icon: channelVo?.icon,
      description: channelType?.description,
    }
    const nlFormComponentsForMobile: NLFormComponent[] = []
    // 移动端不渲染标签组件，还可以忽略管理端定义的表格结构
    tableList.map((item) => {
      if (item.component && item.component.handler !== 'formlabel') {
        nlFormComponentsForMobile.push(item.component)
      }
    })
    return {
      headerData,
      nlFormComponents: nlFormComponentsForMobile
    }
  }, [data])
  
  return {
    data: usefullInfo,
    isLoading,
    refresh: mutate
  }
}