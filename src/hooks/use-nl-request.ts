import type { NLResult } from "#/api";
import useSWR, { type SWRConfiguration } from "swr";
import useSWRImmutable from "swr/immutable";

type ApiUrl = string;
type ApiParams = Record<string, any>
type ApiMethods = 'post' | 'get' | 'delete' | 'put' | 'options';

type NLRequestKeyTuple = [ApiUrl, ApiParams, ApiMethods?];

type Arguments = NLRequestKeyTuple | null | undefined | false;
type NLRequestKey = Arguments | (() => Arguments);

export function useNLRequest<T = any>(key: NLRequestKey, config?: SWRConfiguration) {
  const { data, ...rest} = useSWR<NLResult<T>>(key, config)
  return {
    data: data?.data?.Return,
    ...rest
  }
}

export function useNLRequestImmutable<T = any>(key: NLRequestKey, config?: SWRConfiguration) {
  const { data, ...rest} = useSWRImmutable<NLResult<T>>(key, config)
  return {
    data: data?.data?.Return,
    ...rest
  }
}

export default useNLRequest