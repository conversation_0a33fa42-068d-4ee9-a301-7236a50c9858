import type React from "react";


export interface NLFormHeaderProps {
  channelPath?: string;
  color?: string;
  icon?: string;
  description?: string;
}
const NLFormHeader: React.FC<NLFormHeaderProps> = (props) => {
  const { channelPath, color, icon, description } = props;
  return (
    <div className="bg-white shadow-sm">
      {/* 服务路径导航 */}
      {channelPath && (
        <div className="px-4 py-2 border-b border-gray-100">
          <div className="flex items-center space-x-1 text-xs text-gray-600">
            {channelPath.split(" > ").map((path, index, array) => (
              <div key={`${path}`} className="flex items-center">
                <span className={index === array.length - 1 ? "text-blue-600 font-medium" : ""}>{path}</span>
                {index < array.length - 1 && (
                  <svg
                    className="w-3 h-3 mx-1 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                    aria-hidden="true"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 主要头部信息 */}
      <div className="px-4 py-4">
        <div className="flex items-center space-x-3">
          {/* 服务图标 */}
          <div
            className="w-12 h-12 rounded-lg flex items-center justify-center text-white flex-shrink-0"
            style={{ backgroundColor: color || "#3B82F6" }}
          >
            {icon ? (
              <i className={`${icon} text-lg`} />
            ) : (
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            )}
          </div>

          {/* 服务信息 */}
          <div className="flex-1 min-w-0">
            <h1 className="text-lg font-semibold text-gray-900 mb-1">
              {channelPath ? channelPath.split(" > ").pop() : "服务申请"}
            </h1>

            {description && <p className="text-gray-600 text-xs leading-relaxed line-clamp-2">{description}</p>}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NLFormHeader;
