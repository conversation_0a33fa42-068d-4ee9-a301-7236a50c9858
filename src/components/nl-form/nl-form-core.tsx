import type { NLFormComponent } from '#/entity/process/nl-form-components';
import { Form, type FormProps } from 'antd-mobile';
import type React from 'react';
import nlFormItemsMap, { NLSafetyFormItem } from './nl-form-items';

interface NLFormCoreProps extends FormProps {
  nlFormComponents: NLFormComponent[]
}


const NLFormCore: React.FC<NLFormCoreProps> = (props) => {
  const { nlFormComponents, ...restProps} = props;
  return (
    <Form
      layout='vertical'
      {...restProps}
    >
      {
        nlFormComponents.map((item) => {
          const Component = nlFormItemsMap[item.handler] || NLSafetyFormItem
          return <Component key={item.uuid} nlFormComponent={item} />
        })
      }
    </Form>
  )
}

export default NLFormCore;
