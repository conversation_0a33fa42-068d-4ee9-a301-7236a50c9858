import { Button } from 'antd-mobile';
import type React from 'react';

interface NLFormFooterProps {
  onSaveDraft?: () => void;
  onSubmit?: () => void;
}
const NLFormFooter: React.FC<NLFormFooterProps> = ({onSaveDraft, onSubmit}) => {
  return (
    <div className="mt-6 pt-4 border-t border-gray-200">
      <div className="flex space-x-3">
        <Button color='default' size="large" onClick={onSaveDraft}>
          保存草稿
        </Button>
        <Button color='primary' size="large" onClick={onSubmit}>
          提交申请
        </Button>
      </div>
    </div>
  )
}

export default NLFormFooter;
