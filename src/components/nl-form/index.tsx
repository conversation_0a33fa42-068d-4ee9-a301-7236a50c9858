import type { NLFormComponent } from '#/entity/process/nl-form-components';
import { Button } from 'antd-mobile';
import type React from 'react';

interface NLFormProps {
  channelPath?: string;
  color?: string;
  icon?: string;
  description?: string;
  nlFormComponents?: NLFormComponent[]
}

const NLForm: React.FC<NLFormProps> = (props) => {
  const { channelPath, color, icon, description, nlFormComponents } = props;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部区域 */}
      <div className="bg-white shadow-sm">
        {/* 服务路径导航 */}
        {channelPath && (
          <div className="px-4 py-3 border-b border-gray-100">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              {channelPath.split(' > ').map((path, index, array) => (
                <div key={`${path}`} className="flex items-center">
                  <span className={index === array.length - 1 ? 'text-blue-600 font-medium' : ''}>
                    {path}
                  </span>
                  {index < array.length - 1 && (
                    <svg className="w-4 h-4 mx-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 主要头部信息 */}
        <div className="px-4 py-6">
          <div className="flex items-start space-x-4">
            {/* 服务图标 */}
            <div
              className="w-16 h-16 rounded-xl flex items-center justify-center text-white text-2xl flex-shrink-0 shadow-lg"
              style={{ backgroundColor: color || '#3B82F6' }}
            >
              {icon ? (
                <i className={`${icon} text-2xl`} />
              ) : (
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              )}
            </div>

            {/* 服务信息 */}
            <div className="flex-1 min-w-0">
              <h1 className="text-xl font-bold text-gray-900 mb-2">
                {channelPath ? channelPath.split(' > ').pop() : '服务申请'}
              </h1>

              {description && (
                <p className="text-gray-600 text-sm leading-relaxed">
                  {description}
                </p>
              )}

              {/* 状态标识 */}
              <div className="flex items-center mt-3 space-x-3">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <span className="text-xs text-gray-500">服务可用</span>
                </div>
                <div className="flex items-center space-x-1">
                  <svg className="w-3 h-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-xs text-gray-500">预计处理时间: 1-2个工作日</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 表单内容区域 */}
      <div className="px-4 py-6">
        {/* 表单标题 */}
        <div className="mb-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-2">填写申请信息</h2>
          <p className="text-sm text-gray-600">请完整填写以下信息，我们将尽快为您处理</p>
        </div>

        {/* 表单项渲染区域 */}
        <div className="space-y-4">
          {nlFormComponents && nlFormComponents.length > 0 ? (
            nlFormComponents.map((component, index) => (
              <div key={component.uuid || index} className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                {/* 这里将渲染具体的表单组件 */}
                <div className="text-sm text-gray-500 mb-2">
                  组件类型: {component.handler}
                </div>
                <div className="text-base font-medium text-gray-800">
                  {component.label}
                </div>
                {component.config?.description && (
                  <div className="text-xs text-gray-500 mt-1">
                    {component.config.description}
                  </div>
                )}
                {/* TODO: 根据 component.handler 渲染对应的表单控件 */}
              </div>
            ))
          ) : (
            <div className="bg-white rounded-lg p-8 text-center border border-gray-200">
              <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="text-gray-500">暂无表单项</p>
            </div>
          )}
        </div>

        {/* 底部操作区域 */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <div className="flex space-x-3">
            <Button>
              提交申请
            </Button>
            <Button className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors">
              保存草稿
            </Button>
          </div>
        </div>

        {/* 底部安全区域 */}
        <div className="h-6" />
      </div>
    </div>
  );
};

export default NLForm;
