import type { NLFormComponent } from '#/entity/process/nl-form-components';
import type React from 'react';
import type { NLFormHeaderProps } from './nl-form-header';
import NLFormHeader from './nl-form-header';
import NLFormReminder from './nl-form-reminder';
import NLFormCore from './nl-form-core';
import NLFormEmptyContent from './nl-form-empty-content';
import NLFormFooter from './nl-form-footer';
import { Form } from 'antd-mobile';
import { useCallback } from 'react';

interface NLFormProps extends NLFormHeaderProps {
  nlFormComponents?: NLFormComponent[]
}

const NLForm: React.FC<NLFormProps> = (props) => {
  const { channelPath, color, icon, description, nlFormComponents = [] } = props;
  const [form] = Form.useForm()
  const handleFormFinish = useCallback((value: Record<string, any>) => {
    console.log(value);
  }, [])
  return (
    <div className="min-h-screen bg-gray-50">
      <NLFormHeader channelPath={channelPath} color={color} icon={icon} description={description} />

      <div className="py-4">
        <NLFormReminder />
        {
          (nlFormComponents.length)
            ? <NLFormCore
                nlFormComponents={nlFormComponents}
                form={form}
                onFinish={handleFormFinish}
              /> 
            : <NLFormEmptyContent />
        }

        <NLFormFooter onSubmit={form.submit} />
      </div>
    </div>
  );
};

export default NLForm;
