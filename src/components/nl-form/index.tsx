import type { NLFormComponent } from '#/entity/process/nl-form-components';
import type React from 'react';

interface NLFormProps {
  channelPath?: string;
  color?: string;
  icon?: string;
  description?: string;
  nlFormComponents?: NLFormComponent[]
}
const NLForm: React.FC<NLFormProps> = (props) => {
  const { channelPath, color, icon, description } = props;
  return (
    <div>
      {channelPath}
    </div>
  )
}

export default NLForm;
