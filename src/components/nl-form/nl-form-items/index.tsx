import type { <PERSON><PERSON>orm<PERSON>and<PERSON> } from "#/entity/process/nl-form-components";
import { Form, Input } from "antd-mobile";
import NLFormText from "./nl-formtext";
import type { NLFormItemProps } from "./types";



export default {
  formtext: NLFormText,
  formdivider: () => <div>formdivider</div>,
  formcollapse: () => <div>formcollapse</div>,
  formtab: () => <div>formtab</div>
} as Partial<Record<NLFormHandler, React.FC<NLFormItemProps>>>


/**
 * 兜底组件
 */
export const NLSafetyFormItem: React.FC<NLFormItemProps> = ({nlFormComponent, ...rest}) => {
  return (
    <Form.Item
      key={nlFormComponent.key}
      name={nlFormComponent.key}
      label={nlFormComponent.label}
      help={nlFormComponent.config.description}
      {...rest}
    >
      <Input placeholder="这个是兜底组件" />
    </Form.Item>
  )
}
