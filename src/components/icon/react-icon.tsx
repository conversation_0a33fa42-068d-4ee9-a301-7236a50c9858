import type { CSSProperties } from "react";
// 按需导入具体的图标组件，而不是整个图标库
import {
	BiBarChartAlt2,
	BiBell,
	BiCamera,
	BiCaretDown,
	BiCaretUp,
	BiCertification,
	BiCheck,
	BiCheckDouble,
	BiChevronRight,
	BiCloudUpload,
	BiCodeAlt,
	BiCodeBlock,
	BiCog,
	BiDotsVerticalRounded,
	BiGrid,
	BiGridSmall,
	BiIdCard,
	BiKey,
	BiLink,
	BiLoader,
	BiLock,
	BiLogoAndroid,
	BiPlusCircle,
	BiQuestionMark,
	BiRefresh,
	BiShield,
	BiSolidBuildingHouse,
	BiSolidCopy,
	BiSolidCopyAlt,
	BiSolidErrorAlt,
	BiSolidErrorCircle,
	BiSolidGroup,
	BiSolidHeart,
	BiSolidInfoCircle,
	BiSolidKey,
	BiSolidPencil,
	BiSolidShieldPlus,
	BiSolidTrash,
	BiUser,
	BiWindowClose,
	BiX,
	BiXCircle,
} from "react-icons/bi";
import { FaLink, FaUnlink } from "react-icons/fa";
import { PiLockKeyFill, PiShieldCheckFill, PiUserFill } from "react-icons/pi";
import { TbFileTypeCsv, TbFileTypePdf, TbFileTypeXls, TbFileTypeXml } from "react-icons/tb";
import { VscCloseAll, VscExpandAll } from "react-icons/vsc";

// 图标映射表，用于将 ReactIcon 图标名转换为 react-icons 组件
const iconMap = {
	// 常用图标映射
	"bi:bell": BiBell,
	"bi:check-double": BiCheckDouble,
	"bi:refresh": BiRefresh,
	"bi:x": BiX,
	"bi:chevron-right": BiChevronRight,
	"bi:x-circle": BiXCircle,
	"vsc:close-all": VscCloseAll,
	"bi:grid": BiGrid,
	"bi:grid-small": BiGridSmall,
	"bi:solid-pencil": BiSolidPencil,
	"bi:solid-trash": BiSolidTrash,
	"bi:solid-key": BiSolidKey,
	"bi:solid-copy": BiSolidCopy,
	"bi:cloud-upload": BiCloudUpload,
	"bi:plus-circle": BiPlusCircle,
	"bi:solid-shield-plus": BiSolidShieldPlus,
	"bi:solid-heart": BiSolidHeart,
	"bi:dots-vertical-rounded": BiDotsVerticalRounded,
	"bi:logo-android": BiLogoAndroid,
	"bi:id-card": BiIdCard,
	"bi:key": BiKey,
	"bi:code-alt": BiCodeAlt,
	"bi:solid-copy-alt": BiSolidCopyAlt,
	"bi:lock": BiLock,
	"bi:link": BiLink,
	"bi:certification": BiCertification,
	"bi:shield": BiShield,
	"bi:window-close": BiWindowClose,
	"bi:camera": BiCamera,
	"bi:check": BiCheck,
	"bi:solid-error-alt": BiSolidErrorAlt,
	"bi:solid-error-circle": BiSolidErrorCircle,
	"bi:solid-info-circle": BiSolidInfoCircle,
	"bi:loader": BiLoader,
	"bi:code-block": BiCodeBlock,
	"bi:solid-group": BiSolidGroup,
	"bi:user": BiUser,
	"bi:solid-building-house": BiSolidBuildingHouse,
	"bi:cog": BiCog,
	"bi:caret-down": BiCaretDown,
	"bi:caret-up": BiCaretUp,
	"fa:link": FaLink,
	"fa:unlink": FaUnlink,
	"bi:bar-chart-alt-2": BiBarChartAlt2,
	"tb:file-type-pdf": TbFileTypePdf,
	"tb:file-type-xml": TbFileTypeXml,
	"tb:file-type-csv": TbFileTypeCsv,
	"tb:file-type-xls": TbFileTypeXls,
	"vsc:expand-all": VscExpandAll,
	"pi:user-fill": PiUserFill,
	"pi:lock-key-fill": PiLockKeyFill,
	"pi:shield-check-fill": PiShieldCheckFill,
};

interface ReactIconProps {
	icon: keyof typeof iconMap;
	size?: string | number;
	color?: string;
	className?: string;
	style?: CSSProperties;
	onClick?: (event: any) => void;
}

export default function ReactIcon({
	icon,
	size = "1em",
	color,
	className = "",
	style = {},
	onClick,
	...other
}: ReactIconProps) {
	// 获取对应的 React Icons 组件
	const IconComponent = iconMap[icon] || BiQuestionMark;

	// 转换尺寸到数字或字符串
	const iconSize = typeof size === "string" ? size : `${size}px`;

	// 组合样式
	const iconStyle: CSSProperties = {
		...style,
		color,
	};

	return (
		<div className={`inline-flex align-middle ${className}`} onClick={onClick}>
			<IconComponent size={iconSize} style={iconStyle} {...other} />
		</div>
	);
}