import type { UserProfiles } from '#/entity/auth';
import { useNLRequestImmutable } from '@/hooks/use-nl-request';
import type React from 'react';
import { useState } from 'react';
import {
  BiUser,
  BiChevronRight,
  BiShield,
  BiGroup,
  BiIdCard,
  BiX,
  BiLogOut
} from 'react-icons/bi';
import { Popup, Dialog } from 'antd-mobile';
import { useAuthStore } from '@/store/authStore';

interface UserProfileProps {
  className?: string;
  showName?: boolean;
  size?: 'small' | 'medium' | 'large';
}

const UserProfile: React.FC<UserProfileProps> = ({
  className = '',
  showName = true,
  size = 'medium'
}) => {
  const { data, isLoading } = useNLRequestImmutable<UserProfiles>(['/api/rest/user/get', {}, 'get']);
  const [showModal, setShowModal] = useState(false);
  const { clearAuth } = useAuthStore();

  const sizeClasses = {
    small: 'w-8 h-8 text-sm',
    medium: 'w-10 h-10 text-base',
    large: 'w-12 h-12 text-lg'
  };

  const iconSizeClasses = {
    small: 'text-lg',
    medium: 'text-xl',
    large: 'text-2xl'
  };

  const handleLogout = () => {
    Dialog.confirm({
      title: '确认退出',
      content: '您确定要退出登录吗？',
      confirmText: '退出',
      cancelText: '取消',
      onConfirm: () => {
        clearAuth();
        window.location.href = '/login/student';
      }
    });
  };

  if (isLoading) {
    return (
      <div className={`flex items-center space-x-3 ${className}`}>
        <div className={`${sizeClasses[size]} bg-gray-200 rounded-full animate-pulse`} />
        {showName && <div className="h-4 bg-gray-200 rounded w-20 animate-pulse" />}
      </div>
    );
  }

  return (
    <>
      {/* 用户头像和名称 */}
      <div
        className={`flex items-center space-x-3 cursor-pointer group ${className}`}
        onClick={() => setShowModal(true)}
      >
        {/* 头像 */}
        <div className={`
          ${sizeClasses[size]}
          bg-gradient-to-br from-blue-500 to-purple-600
          rounded-full
          flex items-center justify-center
          text-white
          shadow-lg
          group-hover:shadow-xl
          group-hover:scale-105
          transition-all duration-200
          ring-2 ring-white/20
        `}>
          <BiUser className={iconSizeClasses[size]} />
        </div>

        {/* 用户名 */}
        {showName && (
          <div className="flex items-center space-x-1 group-hover:text-blue-600 transition-colors">
            <span className="font-medium text-gray-800 truncate max-w-24">
              {data?.userName || '用户'}
            </span>
            <BiChevronRight className="text-gray-400 group-hover:text-blue-500 transition-colors" />
          </div>
        )}
      </div>

      {/* 用户详情模态框 */}
      <Popup
        visible={showModal}
        onMaskClick={() => setShowModal(false)}
        onClose={() => setShowModal(false)}
        position="bottom"
        bodyClassName="p-0"
      >
        <div className="bg-white rounded-t-3xl overflow-hidden">
          {/* 头部 */}
          <div className="relative bg-gradient-to-br from-blue-500 to-purple-600 px-6 py-8">
            <button
              type="button"
              onClick={() => setShowModal(false)}
              className="absolute top-4 right-4 text-white/80 hover:text-white transition-colors"
            >
              <BiX className="text-2xl" />
            </button>

            <div className="flex flex-col items-center text-white">
              <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center mb-4 backdrop-blur-sm">
                <BiUser className="text-4xl" />
              </div>
              <h2 className="text-xl font-bold mb-1">{data?.userName}</h2>
              <p className="text-blue-100 text-sm">用户ID: {data?.userId}</p>
            </div>
          </div>

          {/* 内容区域 */}
          <div className="px-6 py-6 space-y-6 h-[50vh] overflow-auto">
            {/* 基本信息 */}
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <BiIdCard className="mr-2 text-blue-500" />
                基本信息
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-600">姓名</span>
                  <span className="font-medium">{data?.name || data?.userName}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-600">用户ID</span>
                  <span className="font-medium font-mono text-sm">{data?.userId}</span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-gray-600">状态</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    data?.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {data?.isActive ? '活跃' : '非活跃'}
                  </span>
                </div>
                {data?.vipLevel !== undefined && (
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-gray-600">VIP等级</span>
                    <span className="font-medium">{data.vipLevel}</span>
                  </div>
                )}
              </div>
            </div>

            {/* 角色信息 */}
            {data?.roleList && data.roleList.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <BiShield className="mr-2 text-green-500" />
                  角色权限
                </h3>
                <div className="space-y-2">
                  {data.roleList.map((role, index) => (
                    <div key={role.uuid || index} className="bg-gray-50 rounded-lg p-3">
                      <div className="font-medium text-gray-800">{role.name}</div>
                      {role.description && (
                        <div className="text-sm text-gray-600 mt-1">{role.description}</div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 团队信息 */}
            {data?.teamList && data.teamList.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                  <BiGroup className="mr-2 text-orange-500" />
                  所属团队
                </h3>
                <div className="space-y-2">
                  {data.teamList.map((team, index) => (
                    <div key={team.uuid || index} className="bg-gray-50 rounded-lg p-3">
                      <div className="font-medium text-gray-800">{team.name}</div>
                      <div className="text-sm text-gray-600 mt-1">
                        成员数量: {team.userCount}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* 退出登录按钮 */}
            <div className="pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={handleLogout}
                className="w-full flex items-center justify-center space-x-2 py-3 px-4 bg-red-50 hover:bg-red-100 text-red-600 rounded-lg transition-colors"
              >
                <BiLogOut className="text-lg" />
                <span className="font-medium">退出登录</span>
              </button>
            </div>
          </div>
        </div>
      </Popup>
    </>
  );
};

export default UserProfile;
