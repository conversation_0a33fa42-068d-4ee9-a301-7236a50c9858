import { Skeleton } from 'antd-mobile';
import type React from 'react';

const Loading: React.FC = () => {

  return (
    <div className="nroad-loader">
    </div>
  )
}

export const FullScreenLoading = () => {
  return (
    <div className='absolute top-0 left-0 right-0 bottom-0 flex justify-center items-center'>
      <Loading />
    </div>
  )
}

export const SkeletonLoading = ({lineCount = 5}: {lineCount?: number}) => {
  return (
    <div>
      <Skeleton.Title animated />
      <Skeleton.Paragraph lineCount={lineCount} animated />
    </div>
  )
}

export default Loading;
