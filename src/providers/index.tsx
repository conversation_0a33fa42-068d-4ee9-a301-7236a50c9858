import axios from '@/utils/axios';
import type React from 'react';
import type { PropsWithChildren } from 'react';
import { SWRConfig } from 'swr'

// biome-ignore lint/style/useTemplate: <explanation>
const nlProxyBaseUrl = import.meta.env.VITE_APP_API_BASE_URL + '/v1/proxy/neatlogic';
const Providers: React.FC<PropsWithChildren> = ({ children }) => {

  return (
    <SWRConfig
      value={{
        fetcher: ([key, params]) => axios({ method: 'get', url: key, params, baseURL: nlProxyBaseUrl }).then(res => res.data)
      }}
    >
      {children}
    </SWRConfig>
  )
}

export default Providers;
