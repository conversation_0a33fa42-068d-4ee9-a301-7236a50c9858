import axios from '@/utils/axios';
import type { AxiosRequestConfig } from 'axios';
import type React from 'react';
import type { PropsWithChildren } from 'react';
import { SWRConfig } from 'swr'

// biome-ignore lint/style/useTemplate: <explanation>
const nlProxyBaseUrl = import.meta.env.VITE_APP_API_BASE_URL + '/v1/proxy/neatlogic';
const Providers: React.FC<PropsWithChildren> = ({ children }) => {
  return (
    <SWRConfig
      value={{
        fetcher: async ([url, params, method = 'post']) => {
          const cfg: AxiosRequestConfig = {
            method,
            url,
            baseURL: nlProxyBaseUrl,
            data: params,
          }
          if (method === 'get' || method === 'delete') {
            cfg.params = params
          }
          const { data } = await axios(cfg)
          return data
        }
      }}
    >
      {children}
    </SWRConfig>
  )
}

export default Providers;
