import type React from 'react';
import useSWR from 'swr';
import type { NLPaginationResult} from '#/api'
import type { NLSelectData} from '#/entity'

const Protected: React.FC = () => {
  const {data} = useSWR<NLPaginationResult<{list: NLSelectData[]}>>(['/api/rest/process/channel/search/forselect'], {
    shouldRetryOnError: false
  })
  console.log(data?.data.Return);
  return (
    <div>
      <h3>protected page</h3>
    </div>
  )
}

export default Protected;
