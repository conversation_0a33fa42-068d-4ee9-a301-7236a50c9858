import type React from 'react';
import { useParams } from 'react-router';
import NLForm from '@/components/nl-form';
import useNLProcessFormdata from '@/hooks/use-nl-process-formdata';
import { SkeletonLoading } from '@/components/Loading';

const ProcessCreatePage: React.FC = () => {
  const params = useParams<{channel_uuid: string}>()
  const channelUuid = params.channel_uuid;
  const {data, isLoading } = useNLProcessFormdata(channelUuid)
  if (isLoading) {
    return <SkeletonLoading />
  }
  return (
    <div className='bg-page-bg min-h-screen'>
      <NLForm
        {...data.headerData}
        nlFormComponents={data.nlFormComponents}
      />
    </div>
  )
}

export default ProcessCreatePage;
