import type React from 'react';
import { useState, useEffect } from 'react';
import { Form, Input, Button } from 'antd-mobile';
import { FaUser, FaLock } from 'react-icons/fa';
import type { NormalAuthorizationParams } from '#/entity/auth';


interface LoginFormProps {
  onSubmit: (values: NormalAuthorizationParams) => Promise<void>;
}

const LoginForm: React.FC<LoginFormProps> = ({ onSubmit }) => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [focusedField, setFocusedField] = useState<string | null>(null);

  const handleFinish = async (values: NormalAuthorizationParams) => {
    setLoading(true);
    try {
      await onSubmit(values);
    } catch (error) {
      console.error('登录失败:', error);
    } finally {
      setLoading(false);
    }
  };


  // 添加键盘事件处理
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === 'Enter' && !loading) {
        form.submit();
      }
    };

    document.addEventListener('keypress', handleKeyPress);
    return () => document.removeEventListener('keypress', handleKeyPress);
  }, [form, loading]);

  return (
    <Form
      form={form}
      onFinish={handleFinish}
      style={{
        '--border-top': 'none',
        '--border-bottom': 'none',
        '--border-inner': 'none',
      }}
    >
      <Form.Item
        name="username"
        rules={[
          { required: true, message: '请输入用户名' },
        ]}
      >
        <div className={`input-wrapper ${focusedField === 'username' ? 'focused' : ''}`}>
          <FaUser className="input-icon" />
          <Input
            placeholder="用户名"
            clearable
            onFocus={() => setFocusedField('username')}
            onBlur={() => setFocusedField(null)}
          />
        </div>
      </Form.Item>

      <Form.Item
        name="password"
        rules={[
          { required: true, message: '请输入密码' },
        ]}
      >
        <div className={`input-wrapper ${focusedField === 'password' ? 'focused' : ''}`}>
          <FaLock className="input-icon" />
          <Input
            placeholder="密码"
            type="password"
            clearable
            onFocus={() => setFocusedField('password')}
            onBlur={() => setFocusedField(null)}
          />
        </div>
      </Form.Item>

      <Form.Item>
        <Button
          color="primary"
          loading={loading}
          block
          size="large"
          onClick={() => form.submit()}
        >
          登录
        </Button>
      </Form.Item>
    </Form>
  );
};

export default LoginForm; 