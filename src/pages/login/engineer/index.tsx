import type { SSOAuthorizationParams, NormalAuthorizationParams } from '#/entity/auth';
import { loginWithIdp, loginWithPassword } from '@/api/auth';
import useSSO from '@/hooks/use-sso';
import { useAuthStore } from '@/store/authStore';
import type React from 'react';
import { useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router';
import { LoginForm } from '../components';
import { Card, Divider } from 'antd-mobile';
import '../index.css'

const EngineerLogin: React.FC = () => {

  const { id, ou, type} = useSSO();
  const { setAuth } = useAuthStore();
  const navigate = useNavigate()
  
  const login = useCallback(async (params: SSOAuthorizationParams) => {
    try {
      const { data } = await loginWithIdp(params)
      const token = data.data?.token
      const userRole = data.data?.role || []
      const isStudent = userRole.includes('student');
      if (token && !isStudent) { // 返回了 token，并且不是学生，表示该身份可以直接登录
        setAuth(token)
        navigate('/')
      }
    } catch (error) {
      console.log(error);
    }
  }, [setAuth, navigate])

  useEffect(() => {
    if (id && ou && type) {
      login({
        ssoAppType: type,
        ssoId: id,
        ou
      })
    }
  }, [id, ou, type, login])

  const handleLoginFormSubmit = useCallback(async (value: NormalAuthorizationParams) => {
    if (!(ou && type)) {
      return
    }
    try {
      const res = await loginWithPassword({
        ...value,
        appType: type,
        ou,
        userId: id,
      })
      const token = res.data.data.token
      if (token) {
        setAuth(token)
        navigate('/')
      }
    } catch (error) {
      console.log('异常：', error);
    }
  }, [setAuth, navigate, ou, type, id])
  return (
    <div className="login-container bg-page-bg">
      <Card className="login-card">
        <div className="login-header">
          <h2 className="login-title">欢迎登录</h2>
          <Divider />
        </div>
        <LoginForm onSubmit={handleLoginFormSubmit} />
      </Card>
    </div>
  )
}

export default EngineerLogin;
