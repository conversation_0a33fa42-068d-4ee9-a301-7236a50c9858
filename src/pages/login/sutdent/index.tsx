import type { SSOAuthorizationParams } from '#/entity/auth';
import { anonymousLogin, loginWithIdp } from '@/api/auth';
import useSSO from '@/hooks/use-sso';
import { useAuthStore } from '@/store/authStore';
import type React from 'react';
import { useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router';
import '../index.css'
import { FullScreenLoading } from '@/components/Loading';

const StudentLogin: React.FC = () => {

  const { id, ou, type} = useSSO();
  const { setAuth } = useAuthStore();
  const navigate = useNavigate()

  const login = useCallback(async (params: SSOAuthorizationParams) => {
    try {
      const { data } = await loginWithIdp(params)
      const userId = data.data?.userId;
      const token = data.data?.token
      console.log(data);
      if (token) { // 返回了 token，表示该身份可以直接登录
        setAuth(token)
        navigate('/')
      } else if (userId) { // 没有 token，表示这个是一个新用户，则直接用这个 userId 进行匿名登录
        const { data: anonymousResData } = await anonymousLogin({
          userId,
          appType: params.ssoAppType,
          ou: params.ou
        })
        if (anonymousResData.data?.token) {
          setAuth(anonymousResData.data?.token)
          navigate('/')
        }
      }
    } catch (error) {
      console.log(error);
    }
  }, [setAuth, navigate])

  useEffect(() => {
    if (id && ou && type) {
      login({
        ssoAppType: type,
        ssoId: id,
        ou
      })
    }
  }, [id, ou, type, login])

  return (
    <FullScreenLoading />
  )
}

export default StudentLogin;
