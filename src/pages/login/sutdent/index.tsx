import type { SSOAuthorizationParams } from '#/entity/auth';
import { anonymousLogin, loginWithIdp } from '@/api/auth';
import useSSO from '@/hooks/use-sso';
import { useAuthStore } from '@/store/authStore';
import type React from 'react';
import { useCallback, useEffect, useState } from 'react';
import { useNavigate } from 'react-router';
import '../index.css'
import { FullScreenLoading } from '@/components/Loading';

const StudentLogin: React.FC = () => {

  const { id, ou, type} = useSSO();
  const { setAuth } = useAuthStore();
  const navigate = useNavigate()
  const [isLoggingIn, setIsLoggingIn] = useState(false); // 添加登录状态控制
  
  const login = useCallback(async (params: SSOAuthorizationParams) => {
    if (isLoggingIn) {
      console.log('正在登录中，跳过重复请求');
      return;
    }

    setIsLoggingIn(true);
    try {
      const { data } = await loginWithIdp(params)
      const userId = data.data?.userId;
      const token = data.data?.token
      console.log(data);
      if (token) { // 返回了 token，表示该身份可以直接登录
        setAuth(token)
        navigate('/')
      } else if (userId) { // 没有 token，表示这个是一个新用户，则直接用这个 userId 进行匿名登录
        const { data: anonymousResData } = await anonymousLogin({
          userId,
          appType: params.ssoAppType,
          ou: params.ou
        })
        if (anonymousResData.data?.token) {
          setAuth(anonymousResData.data?.token)
          navigate('/')
        }
      }
    } catch (error) {
      console.log(error);
    } finally {
      setIsLoggingIn(false);
    }
  }, [setAuth, navigate, isLoggingIn])

  useEffect(() => {
    if (id && ou && type) {
      console.log('登录中...', id, ' ', ou, ' ', type);
      login({
        ssoAppType: type,
        ssoId: id,
        ou
      })
    }
  }, [id, ou, type, login])

  return (
    <FullScreenLoading />
  )
}

export default StudentLogin;
