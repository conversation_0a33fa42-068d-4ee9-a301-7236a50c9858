.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, var(--color-brand) 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  --adm-color-background: 'transparent'
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

.login-card {
  width: 100%;
  max-width: 400px;
  border-radius: 24px;
  overflow: hidden;
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  box-shadow:
    0 32px 64px rgba(0, 0, 0, 0.12),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-brand) 0%, #764ba2 100%);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
  padding-top: 32px;
}

.login-title {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, var(--color-brand) 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.5px;
}

.login-form {
  padding: 0 40px 40px;
}

.login-form .adm-form-item {
  margin-bottom: 24px;
}

.login-form .adm-form-item:last-child {
  margin-bottom: 0;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16px;
  border: 2px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.input-wrapper:hover {
  border-color: rgba(99, 102, 241, 0.3);
  background: rgba(248, 250, 252, 1);
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.1);
}

.input-wrapper:focus-within {
  border-color: var(--color-brand);
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
  box-shadow:
    0 12px 32px rgba(99, 102, 241, 0.15),
    0 0 0 4px rgba(99, 102, 241, 0.1);
}

.input-icon {
  position: absolute;
  left: 16px;
  z-index: 2;
  color: #64748b;
  font-size: 18px;
  transition: all 0.3s ease;
}

.input-wrapper:focus-within .input-icon {
  color: var(--color-brand);
  transform: scale(1.1);
}

.input-wrapper .adm-input {
  padding: 16px 20px 16px 52px !important;
  border: none !important;
  background: transparent !important;
  font-size: 16px;
  font-weight: 500;
  color: #1e293b;
  border-radius: 0 !important;
}

.input-wrapper .adm-input::placeholder {
  color: #94a3b8;
  font-weight: 400;
}

.input-wrapper .adm-input:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* 登录按钮样式 */
.login-form .adm-form-item:last-child .adm-button {
  height: 56px !important;
  border-radius: 16px !important;
  background: linear-gradient(135deg, var(--color-brand) 0%, #764ba2 100%) !important;
  border: none !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px;
  box-shadow:
    0 8px 32px rgba(102, 126, 234, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1) inset !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative;
  overflow: hidden;
}

.login-form .adm-form-item:last-child .adm-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-form .adm-form-item:last-child .adm-button:hover {
  transform: translateY(-2px) !important;
  box-shadow:
    0 12px 40px rgba(102, 126, 234, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.2) inset !important;
}

.login-form .adm-form-item:last-child .adm-button:hover::before {
  left: 100%;
}

.login-form .adm-form-item:last-child .adm-button:active {
  transform: translateY(0) !important;
}

/* 分割线样式 */
.login-header .adm-divider {
  margin: 24px 0 0 0 !important;
  border-color: rgba(226, 232, 240, 0.6) !important;
}

/* 表单验证错误样式 */
.adm-form-item-feedback-error {
  color: #ef4444 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  margin-top: 8px !important;
  padding-left: 16px !important;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }

  .login-card {
    max-width: 100%;
    border-radius: 20px;
  }

  .login-form {
    padding: 0 24px 32px;
  }

  .login-title {
    font-size: 24px;
  }

  .input-wrapper .adm-input {
    padding: 14px 16px 14px 48px !important;
    font-size: 16px;
  }

  .input-icon {
    left: 14px;
    font-size: 16px;
  }
}

/* 加载状态优化 */
.login-form .adm-button-loading .adm-loading {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* 清除按钮样式优化 */
.input-wrapper .adm-input-clear {
  color: #94a3b8 !important;
  right: 16px !important;
}

.input-wrapper .adm-input-clear:hover {
  color: var(--color-brand) !important;
}

/* 高级动画效果 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 应用动画 */
.login-card {
  animation: fadeInScale 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.login-title {
  animation: slideInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

.login-form .adm-form-item:nth-child(1) {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
}

.login-form .adm-form-item:nth-child(2) {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.5s both;
}

.login-form .adm-form-item:nth-child(3) {
  animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.6s both;
}

/* 背景装饰元素 */
.login-container::after {
  content: '';
  position: absolute;
  top: 10%;
  right: 10%;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
  z-index: 0;
}

/* 输入框聚焦时的标签动画 */
.input-wrapper {
  position: relative;
}

.input-wrapper::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--color-brand), #764ba2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(-50%);
}

.input-wrapper:focus-within::after {
  width: 100%;
}

/* 提升整体视觉层次 */
.login-form .adm-form-item {
  position: relative;
  z-index: 1;
}

/* 优化错误状态样式 */
.input-wrapper.error {
  border-color: #ef4444 !important;
  background: rgba(254, 242, 242, 0.8) !important;
}

.input-wrapper.error .input-icon {
  color: #ef4444 !important;
}

.input-wrapper.error:focus-within {
  box-shadow:
    0 12px 32px rgba(239, 68, 68, 0.15),
    0 0 0 4px rgba(239, 68, 68, 0.1) !important;
}