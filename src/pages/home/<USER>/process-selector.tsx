import type { NLCatagory } from '#/entity/process';
import { Popup, type PopupProps } from 'antd-mobile';
import type React from 'react';

interface ProcessSelectorProps extends PopupProps {
  catatories?: NLCatagory[];
}
const ProcessSelector: React.FC<ProcessSelectorProps> = ({ catatories, ...restProps}) => {
  if (!catatories) {
    return null
  }
  return (
    <Popup
      {...restProps}
    >
      {
        catatories.map(catagory => {
          return (
            <div key={catagory.uuid}>
              <h3>{catagory.name}</h3>
              <ul>
                {
                  catagory.list.map(channel => {
                    return (
                      <li key={channel.uuid}>
                        {channel.name}
                      </li>
                    )
                  })
                }
              </ul>
            </div>
          )
        })
      }
    </Popup>
  )
}

export default ProcessSelector;
