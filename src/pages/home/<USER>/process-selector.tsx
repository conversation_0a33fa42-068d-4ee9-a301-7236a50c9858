import type { NLCatagory, NLChannel } from '#/entity/process';
import { Popup, type PopupProps } from 'antd-mobile';
import type React from 'react';
import ReactIcon from '@/components/icon/react-icon';

interface ProcessSelectorProps extends PopupProps {
  catatories?: NLCatagory[];
  onChannelSelect?: (channel: NLChannel) => void;
}

const ProcessSelector: React.FC<ProcessSelectorProps> = ({
  catatories,
  onChannelSelect,
  ...restProps
}) => {

  if (!catatories) {
    return null;
  }

  const handleChannelClick = (channel: NLChannel) => {
    onChannelSelect?.(channel);
  };

  return (
    <Popup
      {...restProps}
      bodyStyle={{
        borderTopLeftRadius: '20px',
        borderTopRightRadius: '20px',
        padding: 0,
        maxHeight: '80vh',
        overflow: 'hidden'
      }}
    >
      <div className="bg-white">
        {/* 顶部标题栏 */}
        <div className="sticky top-0 bg-white border-b border-gray-100 px-6 py-4 z-10">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-800">选择服务类型</h2>
            <div className="w-8 h-1 bg-gray-300 rounded-full mx-auto" />
          </div>
        </div>

        {/* 内容区域 */}
        <div className="px-4 pb-6 max-h-[calc(80vh-80px)] overflow-y-auto">
          {catatories.map((category) => (
            <div key={category.uuid} className="mb-6">
              {/* 分类标题 */}
              <div className="flex items-center mb-4 px-2">
                <div className="w-1 h-5 bg-blue-500 rounded-full mr-3" />
                <h3 className="text-base font-medium text-gray-800">{category.name}</h3>
                <div className="flex-1 h-px bg-gray-200 ml-4" />
              </div>

              {/* 服务网格 */}
              <div className="grid grid-cols-2 gap-3">
                {category.list.map((channel) => (
                  <div
                    key={channel.uuid}
                    onClick={() => handleChannelClick(channel)}
                    className="bg-white border border-gray-200 rounded-xl p-4 hover:border-blue-300 hover:shadow-md transition-all duration-200 active:scale-95 cursor-pointer"
                  >
                    {/* 图标和颜色指示器 */}
                    <div className="flex items-center justify-between mb-3">
                      <div
                        className="w-10 h-10 rounded-lg flex items-center justify-center text-white text-lg"
                        style={{ backgroundColor: channel.color || '#3B82F6' }}
                      >
                        {channel.icon ? (
                          <i className={`${channel.icon} text-xl`} />
                        ) : (
                          <ReactIcon icon="bi:cog" size="20px" />
                        )}
                      </div>
                      {channel.isFavorite === 1 && (
                        <div className="w-2 h-2 bg-red-500 rounded-full" />
                      )}
                    </div>

                    {/* 服务名称 */}
                    <h4 className="text-sm font-medium text-gray-800 mb-1 line-clamp-2">
                      {channel.name}
                    </h4>

                    {/* 服务描述 */}
                    {channel.desc && (
                      <p className="text-xs text-gray-500 line-clamp-2">
                        {channel.desc}
                      </p>
                    )}

                    {/* 底部状态指示器 */}
                    <div className="flex items-center justify-between mt-3">
                      <div className="flex items-center space-x-1">
                        {channel.isActive === 1 ? (
                          <div className="w-2 h-2 bg-green-500 rounded-full" />
                        ) : (
                          <div className="w-2 h-2 bg-gray-400 rounded-full" />
                        )}
                        <span className="text-xs text-gray-500">
                          {channel.isActive === 1 ? '可用' : '维护中'}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}

          {/* 底部安全区域 */}
          <div className="h-4" />
        </div>
      </div>
    </Popup>
  );
};

export default ProcessSelector;
