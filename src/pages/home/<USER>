import UserProfile from '@/components/user-profile';
import useNLRequest from '@/hooks/use-nl-request';
import type React from 'react';
import type { NLChannelSearchResponse, NLChannel } from '#/entity/process'
import { Button } from 'antd-mobile';
import ProcessSelector from './components/process-selector';
import usePopup from '@/hooks/ui/use-popup';
import { useNavigate } from 'react-router';

const HomePage: React.FC = () => {
  const navigate = useNavigate();

  const {data} = useNLRequest<NLChannelSearchResponse>(['/api/rest/catalog/channel/search/mobile', {
    catalogUuid: 0
  }], {
    shouldRetryOnError: false
  })
  const catagories = data?.list;

  const {popupProps, open: openProcessSelector, close: closeProcessSelector} = usePopup()

  const handleClickNewWorkOrder = () => {
    openProcessSelector()
  }

  const handleChannelSelect = (channel: NLChannel) => {
    console.log('选择了服务:', channel);
    closeProcessSelector();
    // 这里可以导航到具体的工单创建页面
    // navigate(`/process/create/${channel.uuid}`);
  }
  return (
    <div className='with-safe-area bg-gray-50 min-h-screen w-full'>
      {/* 顶部导航栏 */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-4 py-3 flex items-center justify-between">
          <h1 className="text-xl font-bold text-gray-800">首页</h1>
          <UserProfile size="medium" />
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="p-4">
        {/* 快速操作按钮 */}
        <div className="mb-6">
          <Button
            onClick={handleClickNewWorkOrder}
            type='button'
            fill='solid'
            color='primary'
            size="large"
            className="w-full h-14 text-lg font-medium rounded-xl shadow-lg"
            style={{
              background: 'linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%)',
              border: 'none'
            }}
          >
            🛠️ 我要报修
          </Button>
        </div>

        {/* 示例卡片 */}
        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="font-semibold text-gray-800 mb-2">功能模块 1</h3>
            <p className="text-gray-600 text-sm">这里是功能模块的描述信息</p>
          </div>
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="font-semibold text-gray-800 mb-2">功能模块 2</h3>
            <p className="text-gray-600 text-sm">这里是功能模块的描述信息</p>
          </div>
        </div>
      </div>

      <ProcessSelector
        {...popupProps}
        catatories={catagories}
        onChannelSelect={handleChannelSelect}
      />
    </div>
  )
}

export default HomePage;
