import UserProfile from '@/components/user-profile';
import useNLRequest from '@/hooks/use-nl-request';
import type React from 'react';

const HomePage: React.FC = () => {

  const {data} = useNLRequest(['/api/rest/process/channel/search/forselect'], {
    shouldRetryOnError: false
  })
  console.log(data);
  return (
    <div className='with-safe-area bg-gray-50 min-h-screen w-full'>
      {/* 顶部导航栏 */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-4 py-3 flex items-center justify-between">
          <h1 className="text-xl font-bold text-gray-800">首页</h1>
          <UserProfile size="medium" />
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="p-4">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">欢迎回来！</h2>
          <p className="text-gray-600">
            点击右上角的头像可以查看您的详细信息和管理账户设置。
          </p>
        </div>

        {/* 示例卡片 */}
        <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="font-semibold text-gray-800 mb-2">功能模块 1</h3>
            <p className="text-gray-600 text-sm">这里是功能模块的描述信息</p>
          </div>
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="font-semibold text-gray-800 mb-2">功能模块 2</h3>
            <p className="text-gray-600 text-sm">这里是功能模块的描述信息</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default HomePage;
