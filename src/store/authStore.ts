import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface AuthState {
  // 用户是否已认证
  isAuthenticated: boolean;
  // token
  token: string | null;
  // 设置认证状态
  setAuth: (token: string) => void;
  // 清除认证状态
  clearAuth: () => void;
  // 检查认证状态
  checkAuth: () => boolean;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      isAuthenticated: false,
      token: null,
      setAuth: (token: string) => set({
        isAuthenticated: true,
        token,
      }),
      
      clearAuth: () => set({
        isAuthenticated: false,
        token: null,
      }),
      
      checkAuth: () => {
        const { isAuthenticated, token } = get();
        
        // 如果未认证或没有token，直接返回false
        if (!isAuthenticated || !token) {
          return false;
        }
        
        return true;
      }
    }),
    {
      name: 'auth-storage',
      // 只持久化这些字段
      partialize: (state) => ({ 
        isAuthenticated: state.isAuthenticated,
        token: state.token,
      }),
    }
  )
); 