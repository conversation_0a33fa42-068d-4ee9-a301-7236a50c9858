import { unstableSetRender } from 'antd-mobile';
import { createRoot } from 'react-dom/client'
import VConsole from 'vconsole';
import App from './App.tsx'
import './index.css'
new VConsole();


unstableSetRender((node, container) => {
  // @ts-ignore
  container._reactRoot ||= createRoot(container);
  // @ts-ignore
  const root = container._reactRoot;
  root.render(node);
  return async () => {
    await new Promise((resolve) => setTimeout(resolve, 0));
    root.unmount();
  };
});
// biome-ignore lint/style/noNonNullAssertion: <explanation>
createRoot(document.getElementById('root')!).render(
  <App />
)
