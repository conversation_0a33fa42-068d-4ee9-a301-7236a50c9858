import type { NLResultWrap } from "#/api";
import type { AnonymousLoginParams, SSOAuthorization, SSOAuthorizationParams, NormalAuthorizationParams } from "#/entity/auth";
import axios from '@/utils/axios';

export const loginWithIdp = ({ssoAppType, ssoId, ou}: SSOAuthorizationParams) => {
  return axios<NLResultWrap<SSOAuthorization>>({
    url: '/v1/login/sso',
    method: 'post',
    data: {
      code: ssoId,
      ou,
      appType: ssoAppType
    }
  })
}

export const anonymousLogin = (params: AnonymousLoginParams) => axios<NLResultWrap<SSOAuthorization>>({
  url: '/v1/login/anonymousLogin',
  method: 'post',
  data: params
})

export const loginWithPassword = (params: NormalAuthorizationParams) => axios<NLResultWrap<SSOAuthorization>>({
  url: '/v1/login/bindingAndLogin',
  method: 'POST',
  data: params,
})
