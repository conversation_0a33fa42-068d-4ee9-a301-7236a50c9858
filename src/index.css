@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --color-brand: #2F54D4;
    --color-t-primary: #333;
    --color-t-secont: #666;
    --color-t-description: #999;
    --color-divider: #F0F0F0; 
    --color-page-bg: #F4F4F4;
    --color-success: #01EE87;
    --color-warning: #ff9900;
    --color-error: #ff2900;
    --color-info: #f4f4f4;
  }
}

html:root {
  --adm-color-background: 'transparent';
}

body {
  margin: 0;
}

@keyframes nroad-loading {
  100% {transform: rotate(1turn)}
}
.nroad-loader {
  width: 50px;
  aspect-ratio: 1;
  border-radius: 50%;
  border: 8px solid #0000;
  border-right-color: var(--color-brand);
  position: relative;
  animation: nroad-loading 1s infinite linear;
}
.nroad-loader:before,
.nroad-loader:after {
  content: "";
  position: absolute;
  inset: -8px;
  border-radius: 50%;
  border: inherit;
  animation: inherit;
  animation-duration: 4s;
}

.with-safe-area {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
}